server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name liveserver.langu.io;


location /.well-known/acme-challenge/ {
        root /var/www/nginx;
    }

    return 301 https://$server_name$request_uri;
}

upstream websocket {
        server node:6020;
}

upstream classroom_websocket {
        server classroom-node:6021;
}

# upstream frontend {
#         server frontend:3000;
# }

# upstream libreoffice {
#         server libreoffice:6040;
# }

map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {
        listen 443 http2 ssl;
        listen [::]:443 http2 ssl;
        client_max_body_size 24000M;

        server_name liveserver.langu.io;

        #ssl_certificate /etc/nginx/nginx-selfsigned.crt;
        #ssl_certificate_key /etc/nginx/nginx-selfsigned.key;
        ssl_certificate /etc/letsencrypt/live/langu.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/langu.io/privkey.pem;
        ssl_session_cache shared:SSL:50m;
        ssl_session_timeout 5m;
        ssl_prefer_server_ciphers on;

        root /var/www/langu/public;

        index index.php index.html index.htm index.nginx-debian.html;

        # location / {
        #         gzip on;
        #         gzip_static on;
        #         try_files $uri /app.php/$is_args$args;
        #         access_log  off;
        # }

        # location /api/ {
        #         error_log /var/www/langu/l2api.error.log;
        #         rewrite ^ /api.php$is_args$args;
        # }

#        location = /.well-known/apple-developer-merchantid-domain-association {
#                alias /var/www/langu/.well-known/apple-developer-merchantid-domain-association;
#                default_type text/plain;
#         }




        location /websocket {
                error_log /var/www/langu/l2-live.error.log;

                gzip off;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection $connection_upgrade;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $host;
                proxy_pass http://websocket;
                proxy_cache_bypass $http_upgrade;
        }

        location /websocket-classroom {
                error_log /var/www/langu/l2-classroom.error.log;

                # Handle preflight OPTIONS requests
                if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' '$http_origin' always;
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
                    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, Cookie, X-Requested-With' always;
                    add_header 'Access-Control-Allow-Credentials' 'true' always;
                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
                }

                gzip off;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection $connection_upgrade;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $host;
                proxy_set_header Origin $http_origin;
                proxy_pass http://classroom_websocket;
                proxy_cache_bypass $http_upgrade;

                # CORS headers for WebSocket
                add_header 'Access-Control-Allow-Origin' '$http_origin' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, Cookie, X-Requested-With' always;
                add_header 'Access-Control-Allow-Credentials' 'true' always;
        }

        # location /teacher-listing {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/teacher-listing;
        #         proxy_cache_bypass $http_upgrade;
        # }


        # location /user/password/ {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/user/password/;
        #         proxy_cache_bypass $http_upgrade;
        # }


        # location /user/settings {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/user/settings;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location /blog {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/blog;
        #         proxy_cache_bypass $http_upgrade;
        # }


        # location /sw.js {
        # #        #auth_basic "L2 dev";
        # #        #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location /checkout/p24-payin/ {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/checkout/p24-payin/;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location /teacher/ {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/teacher/;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location = /user/messages {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/user/messages;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location ~ /user/messages/([0-9]+)/view {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/user/messages/$1/view;
        #         proxy_cache_bypass $http_upgrade;
        # }

#         location  = / {
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend;
#                 proxy_cache_bypass $http_upgrade;
#       }

        # location /_nuxt/ {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/_nuxt/;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location ~ /lesson/([0-9]+)/classroom {
        #         #auth_basic "L2 dev";
        #         #auth_basic_user_file /etc/nginx/.htpasswd;
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host;
        #         proxy_pass http://frontend/lesson/$1/classroom;
        #         proxy_cache_bypass $http_upgrade;
        # }


        # location /user/register {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host:4061;
        #         proxy_pass http://frontend/user/register;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location = /robots.txt {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host:4061;
        #         proxy_pass http://frontend/robots.txt;
        #         proxy_cache_bypass $http_upgrade;
        # }

        # location = /sitemap.xml {
        #         gzip off;
        #         proxy_set_header Upgrade $http_upgrade;
        #         proxy_set_header Connection $connection_upgrade;
        #         proxy_http_version 1.1;
        #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #         proxy_set_header Host $host:4061;
        #         proxy_pass http://frontend/sitemap.xml;
        #         proxy_cache_bypass $http_upgrade;
        # }

#         location /user/lessons {
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/user/lessons;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location /user/unscheduled-lessons {
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/user/unscheduled-lessons;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location /user/past-lessons {
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/user/past-lessons;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location  = /business {
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/business;
#                 proxy_cache_bypass $http_upgrade;
#       }

#         location  = /business/success {
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/business/success;
#                 proxy_cache_bypass $http_upgrade;
#       }

#         location  = /education {
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/education;
#                 proxy_cache_bypass $http_upgrade;
#       }

#         location  = /about-us {
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/about-us;
#                 proxy_cache_bypass $http_upgrade;
#       }

#         location /api/convert {
#                 error_log /var/www/langu/l2-live.error.log;

#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://libreoffice;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location /faq {
#                 error_log /var/www/langu/l2-live.error.log;

#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/faq;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location /user/payments/lessons {
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/user/payments/lessons;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         location /user/payments/payouts {
#                 gzip off;
#                 proxy_set_header Upgrade $http_upgrade;
#                 proxy_set_header Connection $connection_upgrade;
#                 proxy_http_version 1.1;
#                 proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#                 proxy_set_header Host $host;
#                 proxy_pass http://frontend/user/payments/payouts;
#                 proxy_cache_bypass $http_upgrade;
#         }

#         # Application can be run in debug mode by visiting langu.dev/app_debug.php/<other_route>
#         # This location block should be omitted in production.
#         location ~ ^/(app|config)\.php(/|$) {
#                 gzip off;

#                 # Uncomment these lines for staging / production modes.
#                  #auth_basic "L2 dev";
#                  #auth_basic_user_file /etc/nginx/.htpasswd;

#                 fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#                 include fastcgi_params;
#                 fastcgi_pass php:9000;
#                 fastcgi_split_path_info ^(.+\.php)(/.+)$;
#                 fastcgi_buffers 16 16k;
#                 fastcgi_buffer_size 32k;
#     }

#     # This location block should be omitted in production.
#      location ~ ^/api\.php(/|$) {
#                 gzip off;

#                 # Uncomment these lines for staging / production modes.
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;

#                 fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#                 include fastcgi_params;
#                 fastcgi_pass php:9000;
#                 fastcgi_split_path_info ^(.+\.php)(/.+)$;
#                 fastcgi_buffers 16 16k;
#                 fastcgi_buffer_size 32k;
#     }

#     location ~ ^/app\.php(/|$) {
#         gzip off;

#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         include fastcgi_params;
#         fastcgi_pass php:9000;
#         fastcgi_split_path_info ^(.+\.php)(/.+)$;
#         fastcgi_buffers 16 16k;
#         fastcgi_buffer_size 32k;

#         internal;
#     }

#      location ~ ^/api\.php(/|$) {
#         gzip off;

#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         include fastcgi_params;
#         fastcgi_pass php:9000;
#         fastcgi_split_path_info ^(.+\.php)(/.+)$;
#         fastcgi_buffers 16 16k;
#         fastcgi_buffer_size 32k;

#         internal;
#     }

    # Serve static files from uploads directory
    location /uploads/ {
        alias /var/www/langu/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    location ~ \.php$ {
      return 404;
    }

    location ~ /\.ht {
            deny all;
    }

    error_log /var/log/nginx/l2_error.log;
    access_log /var/log/nginx/l2_access.log;

}

# server {
#         listen 443 http2 ssl;
#         listen [::]:443 http2 ssl;
#         client_max_body_size 24000M;

#         server_name admin.langu.io ;

#         #ssl_certificate /etc/nginx/nginx-selfsigned.crt;
#         #ssl_certificate_key /etc/nginx/nginx-selfsigned.key;
#         ssl_certificate /etc/letsencrypt/live/admin.langu.io/fullchain.pem;
#         ssl_certificate_key /etc/letsencrypt/live/admin.langu.io/privkey.pem;
#         ssl_session_cache shared:SSL:50m;
#         ssl_session_timeout 5m;
#         ssl_prefer_server_ciphers on;

#         root /var/www/langu/public;

#         index index.php index.html index.htm index.nginx-debian.html;

#         location / {
#                 gzip on;
#                 gzip_static on;
#                 try_files $uri /admin.php/$is_args$args;
#                 access_log  off;
#         }


#  # Application can be run in debug mode by visiting langu.dev/app_debug.php/<other_route>
#         # This location block should be omitted in production.
#         location ~ ^/(admin|config)\.php(/|$) {
#                 gzip off;

#                 # Uncomment these lines for staging / production modes.
#                 #auth_basic "L2 dev";
#                 #auth_basic_user_file /etc/nginx/.htpasswd;

#                 fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#                 include fastcgi_params;
#                 fastcgi_pass php:9000;
#                 fastcgi_split_path_info ^(.+\.php)(/.+)$;
#                 fastcgi_buffers 16 16k;
#                 fastcgi_buffer_size 32k;
#     }

#     location ~ \.php$ {
#       return 404;
#     }

#     location ~ /\.ht {
#             deny all;
#     }

#     error_log /var/log/nginx/l2admin_error.log;
#     access_log /var/log/nginx/l2admin_access.log;


# }


# server {
#     if ($host = api.langu.io) {
#         return 301 https://$host$request_uri;
#     } # managed by Certbot


#     listen 80 ;
#     listen [::]:80;
#     server_name api.langu.io;

#     return 301 https://$server_name$request_uri;
# }

