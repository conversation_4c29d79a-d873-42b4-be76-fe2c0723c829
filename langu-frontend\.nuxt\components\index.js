import { wrapFunctional } from './utils'

export { default as BuzzDialog } from '../..\\components\\BuzzDialog.vue'
export { default as Calendar } from '../..\\components\\Calendar.vue'
export { default as CalendarDate } from '../..\\components\\CalendarDate.vue'
export { default as CheckEmailDialog } from '../..\\components\\CheckEmailDialog.vue'
export { default as ConfirmDialog } from '../..\\components\\ConfirmDialog.vue'
export { default as CookiePopup } from '../..\\components\\CookiePopup.vue'
export { default as Footer } from '../..\\components\\Footer.vue'
export { default as FreeSlots } from '../..\\components\\FreeSlots.vue'
export { default as GoogleSignInButton } from '../..\\components\\GoogleSignInButton.vue'
export { default as Header } from '../..\\components\\Header.vue'
export { default as LAvatar } from '../..\\components\\LAvatar.vue'
export { default as LChip } from '../..\\components\\LChip.vue'
export { default as LDialog } from '../..\\components\\LDialog.vue'
export { default as LessonTimeNotice } from '../..\\components\\LessonTimeNotice.vue'
export { default as LExpansionPanels } from '../..\\components\\LExpansionPanels.vue'
export { default as Loader } from '../..\\components\\Loader.vue'
export { default as LoadMoreBtn } from '../..\\components\\LoadMoreBtn.vue'
export { default as LoginSidebar } from '../..\\components\\LoginSidebar.vue'
export { default as MessageDialog } from '../..\\components\\MessageDialog.vue'
export { default as Pagination } from '../..\\components\\Pagination.vue'
export { default as SetPasswordDialog } from '../..\\components\\SetPasswordDialog.vue'
export { default as Snackbar } from '../..\\components\\Snackbar.vue'
export { default as StarRating } from '../..\\components\\StarRating.vue'
export { default as Steps } from '../..\\components\\Steps.vue'
export { default as SummaryLessonDialog } from '../..\\components\\SummaryLessonDialog.vue'
export { default as TeacherCard } from '../..\\components\\TeacherCard.vue'
export { default as TeacherFilter } from '../..\\components\\TeacherFilter.vue'
export { default as TeacherFilterNew } from '../..\\components\\TeacherFilterNew.vue'
export { default as TimePicker } from '../..\\components\\TimePicker.vue'
export { default as TimePickerItem } from '../..\\components\\TimePickerItem.vue'
export { default as UserStatus } from '../..\\components\\UserStatus.vue'
export { default as Youtube } from '../..\\components\\Youtube.vue'
export { default as BusinessPage } from '../..\\components\\business-page\\BusinessPage.vue'
export { default as ClassroomAudioItem } from '../..\\components\\classroom\\AudioItem.vue'
export { default as ClassroomContainer } from '../..\\components\\classroom\\ClassroomContainer.vue'
export { default as ClassroomContainerHeader } from '../..\\components\\classroom\\ClassroomContainerHeader.vue'
export { default as ClassroomDropFileArea } from '../..\\components\\classroom\\DropFileArea.vue'
export { default as ClassroomImageItem } from '../..\\components\\classroom\\ImageItem.vue'
export { default as ClassroomKonva } from '../..\\components\\classroom\\Konva.vue'
export { default as ClassroomLibrary } from '../..\\components\\classroom\\Library.vue'
export { default as ClassroomOtherCursor } from '../..\\components\\classroom\\OtherCursor.vue'
export { default as ClassroomPdfItem } from '../..\\components\\classroom\\PdfItem.vue'
export { default as ClassroomTinymceVue } from '../..\\components\\classroom\\TinymceVue.vue'
export { default as ClassroomToolbar } from '../..\\components\\classroom\\Toolbar.vue'
export { default as ClassroomVideoInput } from '../..\\components\\classroom\\VideoInput.vue'
export { default as ClassroomVideoItem } from '../..\\components\\classroom\\VideoItem.vue'
export { default as ClassroomViewport } from '../..\\components\\classroom\\Viewport.vue'
export { default as FormEditor } from '../..\\components\\form\\Editor.vue'
export { default as FormRate } from '../..\\components\\form\\FormRate.vue'
export { default as FormSearchInput } from '../..\\components\\form\\SearchInput.vue'
export { default as FormSelectInput } from '../..\\components\\form\\SelectInput.vue'
export { default as FormSelectInputNew } from '../..\\components\\form\\SelectInputNew.vue'
export { default as FormTextInput } from '../..\\components\\form\\TextInput.vue'
export { default as HomepageAboutSection } from '../..\\components\\homepage\\AboutSection.vue'
export { default as HomepageFaqSection } from '../..\\components\\homepage\\FaqSection.vue'
export { default as HomepageHowWorksSection } from '../..\\components\\homepage\\HowWorksSection.vue'
export { default as HomepageIntroSection } from '../..\\components\\homepage\\IntroSection.vue'
export { default as HomepageLanguagesSection } from '../..\\components\\homepage\\LanguagesSection.vue'
export { default as HomepageReviewSection } from '../..\\components\\homepage\\ReviewSection.vue'
export { default as HomepageSelectLanguage } from '../..\\components\\homepage\\SelectLanguage.vue'
export { default as HomepageStatSection } from '../..\\components\\homepage\\StatSection.vue'
export { default as HomepageThinkingSection } from '../..\\components\\homepage\\ThinkingSection.vue'
export { default as HomepageTutorsSection } from '../..\\components\\homepage\\TutorsSection.vue'
export { default as ImagesAlarmGradientIcon } from '../..\\components\\images\\AlarmGradientIcon.vue'
export { default as ImagesBusinessPageIntroImage } from '../..\\components\\images\\BusinessPageIntroImage.vue'
export { default as ImagesBusinessPageIntroMobileImage } from '../..\\components\\images\\BusinessPageIntroMobileImage.vue'
export { default as ImagesCareerGradientIcon } from '../..\\components\\images\\CareerGradientIcon.vue'
export { default as ImagesCheckedGradientIcon } from '../..\\components\\images\\CheckedGradientIcon.vue'
export { default as ImagesEducationGradientIcon } from '../..\\components\\images\\EducationGradientIcon.vue'
export { default as ImagesEmailIcon } from '../..\\components\\images\\EmailIcon.vue'
export { default as ImagesEnFlagIcon } from '../..\\components\\images\\EnFlagIcon.vue'
export { default as ImagesEsFlagIcon } from '../..\\components\\images\\EsFlagIcon.vue'
export { default as ImagesGoogleIcon } from '../..\\components\\images\\GoogleIcon.vue'
export { default as ImagesHomePageIntroImage } from '../..\\components\\images\\HomePageIntroImage.vue'
export { default as ImagesLifeGradientIcon } from '../..\\components\\images\\LifeGradientIcon.vue'
export { default as ImagesMoonGradientIcon } from '../..\\components\\images\\MoonGradientIcon.vue'
export { default as ImagesPlFlagIcon } from '../..\\components\\images\\PlFlagIcon.vue'
export { default as ImagesSearchIcon } from '../..\\components\\images\\SearchIcon.vue'
export { default as ImagesSunGradientIcon } from '../..\\components\\images\\SunGradientIcon.vue'
export { default as ImagesSunsetGradientIcon } from '../..\\components\\images\\SunsetGradientIcon.vue'
export { default as LandingPageTeachersSlider } from '../..\\components\\landing-page\\TeachersSlider.vue'
export { default as LandingPageTestimonialsSlider } from '../..\\components\\landing-page\\TestimonialsSlider.vue'
export { default as PaymentsCountries } from '../..\\components\\payments\\countries.js'
export { default as PaymentsPaymentDetailsModal } from '../..\\components\\payments\\PaymentDetailsModal.vue'
export { default as PaymentsPaymentItem } from '../..\\components\\payments\\PaymentItem.vue'
export { default as PaymentsPaymentLesson } from '../..\\components\\payments\\PaymentLesson.vue'
export { default as PaymentsPaymentPayout } from '../..\\components\\payments\\PaymentPayout.vue'
export { default as PaymentsPage } from '../..\\components\\payments\\PaymentsPage.vue'
export { default as PaymentsPayoutItem } from '../..\\components\\payments\\PayoutItem.vue'
export { default as PaymentsPayoutModal } from '../..\\components\\payments\\PayoutModal.vue'
export { default as PaymentsSavedAccountsModal } from '../..\\components\\payments\\SavedAccountsModal.vue'
export { default as PaymentsWiseTransferModal } from '../..\\components\\payments\\WiseTransferModal.vue'
export { default as TeacherListing } from '../..\\components\\teacher-listing\\TeacherListing.vue'
export { default as TeacherListingBanner } from '../..\\components\\teacher-listing\\TeacherListingBanner.vue'
export { default as TeacherListingHeader } from '../..\\components\\teacher-listing\\TeacherListingHeader.vue'
export { default as TeacherProfileCourseItem } from '../..\\components\\teacher-profile\\CourseItem.vue'
export { default as TeacherProfileFeedbackTags } from '../..\\components\\teacher-profile\\FeedbackTags.vue'
export { default as TeacherProfileFindMoreTeachersButton } from '../..\\components\\teacher-profile\\FindMoreTeachersButton.vue'
export { default as TeacherProfilePricePerLesson } from '../..\\components\\teacher-profile\\PricePerLesson.vue'
export { default as TeacherProfileSidebar } from '../..\\components\\teacher-profile\\TeacherProfileSidebar.vue'
export { default as TeacherProfileTimePickerDialog } from '../..\\components\\teacher-profile\\TimePickerDialog.vue'
export { default as UserLessonsLessonEvaluationDialog } from '../..\\components\\user-lessons\\LessonEvaluationDialog.vue'
export { default as UserLessonsLessonItem } from '../..\\components\\user-lessons\\LessonItem.vue'
export { default as UserLessonsPage } from '../..\\components\\user-lessons\\LessonsPage.vue'
export { default as UserLessonsPastLesson } from '../..\\components\\user-lessons\\PastLesson.vue'
export { default as UserLessonsTimePickerDialog } from '../..\\components\\user-lessons\\TimePickerDialog.vue'
export { default as UserLessonsUnscheduledLesson } from '../..\\components\\user-lessons\\UnscheduledLesson.vue'
export { default as UserLessonsUpcomingLesson } from '../..\\components\\user-lessons\\UpcomingLesson.vue'
export { default as UserMessagesConversation } from '../..\\components\\user-messages\\Conversation.vue'
export { default as UserMessagesConversationItem } from '../..\\components\\user-messages\\ConversationItem.vue'
export { default as UserMessagesEmptyContent } from '../..\\components\\user-messages\\EmptyContent.vue'
export { default as UserMessagesPage } from '../..\\components\\user-messages\\MessagesPage.vue'
export { default as UserSettingsAboutMeInfo } from '../..\\components\\user-settings\\AboutMeInfo.vue'
export { default as UserSettingsAddQualificationDialog } from '../..\\components\\user-settings\\AddQualificationDialog.vue'
export { default as UserSettingsBackgroundInfo } from '../..\\components\\user-settings\\BackgroundInfo.vue'
export { default as UserSettingsBasicInfo } from '../..\\components\\user-settings\\BasicInfo.vue'
export { default as UserSettingsCalendarNotificationInfo } from '../..\\components\\user-settings\\CalendarNotificationInfo.vue'
export { default as UserSettingsCourseItem } from '../..\\components\\user-settings\\CourseItem.vue'
export { default as UserSettingsCoursesInfo } from '../..\\components\\user-settings\\CoursesInfo.vue'
export { default as UserSettingsIllustrationDialog } from '../..\\components\\user-settings\\IllustrationDialog.vue'
export { default as UserSettingsLanguagesInfo } from '../..\\components\\user-settings\\LanguagesInfo.vue'
export { default as UserSettingsLearningPreferencesInfo } from '../..\\components\\user-settings\\LearningPreferencesInfo.vue'
export { default as UserSettingsLessonPrice } from '../..\\components\\user-settings\\LessonPrice.vue'
export { default as UserSettingsPerLessonPrice } from '../..\\components\\user-settings\\PerLessonPrice.vue'
export { default as UserSettingsPricingTableInfo } from '../..\\components\\user-settings\\PricingTableInfo.vue'
export { default as UserSettingsQualificationSuccessDialog } from '../..\\components\\user-settings\\QualificationSuccessDialog.vue'
export { default as UserSettingsReceiptInfo } from '../..\\components\\user-settings\\ReceiptInfo.vue'
export { default as UserSettingsSpecialityDialog } from '../..\\components\\user-settings\\SpecialityDialog.vue'
export { default as UserSettingsSummaryInfo } from '../..\\components\\user-settings\\SummaryInfo.vue'
export { default as UserSettingsTeachingPreferencesInfo } from '../..\\components\\user-settings\\TeachingPreferencesInfo.vue'
export { default as UserSettingsTeachingQualificationsInfo } from '../..\\components\\user-settings\\TeachingQualificationsInfo.vue'
export { default as UserSettingAutocomplete } from '../..\\components\\user-settings\\UserSettingAutocomplete.vue'
export { default as UserSettingSelect } from '../..\\components\\user-settings\\UserSettingSelect.vue'
export { default as UserSettingTemplate } from '../..\\components\\user-settings\\UserSettingTemplate.vue'
export { default as BusinessPageIconsDotsIcon } from '../..\\components\\business-page\\icons\\DotsIcon.vue'
export { default as ClassroomVideoTokbox } from '../..\\components\\classroom\\video\\Tokbox.vue'
export { default as ClassroomVideoTwilio } from '../..\\components\\classroom\\video\\Twilio.vue'
export { default as ClassroomVideoActions } from '../..\\components\\classroom\\video\\VideoActions.vue'
export { default as ClassroomVideoWhereby } from '../..\\components\\classroom\\video\\Whereby.vue'
export { default as ClassroomVueDraggableResizable } from '../..\\components\\classroom\\vue-draggable-resizable\\VueDraggableResizable.vue'

export const LazyBuzzDialog = import('../..\\components\\BuzzDialog.vue' /* webpackChunkName: "components/buzz-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyCalendar = import('../..\\components\\Calendar.vue' /* webpackChunkName: "components/calendar" */).then(c => wrapFunctional(c.default || c))
export const LazyCalendarDate = import('../..\\components\\CalendarDate.vue' /* webpackChunkName: "components/calendar-date" */).then(c => wrapFunctional(c.default || c))
export const LazyCheckEmailDialog = import('../..\\components\\CheckEmailDialog.vue' /* webpackChunkName: "components/check-email-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyConfirmDialog = import('../..\\components\\ConfirmDialog.vue' /* webpackChunkName: "components/confirm-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyCookiePopup = import('../..\\components\\CookiePopup.vue' /* webpackChunkName: "components/cookie-popup" */).then(c => wrapFunctional(c.default || c))
export const LazyFooter = import('../..\\components\\Footer.vue' /* webpackChunkName: "components/footer" */).then(c => wrapFunctional(c.default || c))
export const LazyFreeSlots = import('../..\\components\\FreeSlots.vue' /* webpackChunkName: "components/free-slots" */).then(c => wrapFunctional(c.default || c))
export const LazyGoogleSignInButton = import('../..\\components\\GoogleSignInButton.vue' /* webpackChunkName: "components/google-sign-in-button" */).then(c => wrapFunctional(c.default || c))
export const LazyHeader = import('../..\\components\\Header.vue' /* webpackChunkName: "components/header" */).then(c => wrapFunctional(c.default || c))
export const LazyLAvatar = import('../..\\components\\LAvatar.vue' /* webpackChunkName: "components/l-avatar" */).then(c => wrapFunctional(c.default || c))
export const LazyLChip = import('../..\\components\\LChip.vue' /* webpackChunkName: "components/l-chip" */).then(c => wrapFunctional(c.default || c))
export const LazyLDialog = import('../..\\components\\LDialog.vue' /* webpackChunkName: "components/l-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyLessonTimeNotice = import('../..\\components\\LessonTimeNotice.vue' /* webpackChunkName: "components/lesson-time-notice" */).then(c => wrapFunctional(c.default || c))
export const LazyLExpansionPanels = import('../..\\components\\LExpansionPanels.vue' /* webpackChunkName: "components/l-expansion-panels" */).then(c => wrapFunctional(c.default || c))
export const LazyLoader = import('../..\\components\\Loader.vue' /* webpackChunkName: "components/loader" */).then(c => wrapFunctional(c.default || c))
export const LazyLoadMoreBtn = import('../..\\components\\LoadMoreBtn.vue' /* webpackChunkName: "components/load-more-btn" */).then(c => wrapFunctional(c.default || c))
export const LazyLoginSidebar = import('../..\\components\\LoginSidebar.vue' /* webpackChunkName: "components/login-sidebar" */).then(c => wrapFunctional(c.default || c))
export const LazyMessageDialog = import('../..\\components\\MessageDialog.vue' /* webpackChunkName: "components/message-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyPagination = import('../..\\components\\Pagination.vue' /* webpackChunkName: "components/pagination" */).then(c => wrapFunctional(c.default || c))
export const LazySetPasswordDialog = import('../..\\components\\SetPasswordDialog.vue' /* webpackChunkName: "components/set-password-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazySnackbar = import('../..\\components\\Snackbar.vue' /* webpackChunkName: "components/snackbar" */).then(c => wrapFunctional(c.default || c))
export const LazyStarRating = import('../..\\components\\StarRating.vue' /* webpackChunkName: "components/star-rating" */).then(c => wrapFunctional(c.default || c))
export const LazySteps = import('../..\\components\\Steps.vue' /* webpackChunkName: "components/steps" */).then(c => wrapFunctional(c.default || c))
export const LazySummaryLessonDialog = import('../..\\components\\SummaryLessonDialog.vue' /* webpackChunkName: "components/summary-lesson-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherCard = import('../..\\components\\TeacherCard.vue' /* webpackChunkName: "components/teacher-card" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherFilter = import('../..\\components\\TeacherFilter.vue' /* webpackChunkName: "components/teacher-filter" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherFilterNew = import('../..\\components\\TeacherFilterNew.vue' /* webpackChunkName: "components/teacher-filter-new" */).then(c => wrapFunctional(c.default || c))
export const LazyTimePicker = import('../..\\components\\TimePicker.vue' /* webpackChunkName: "components/time-picker" */).then(c => wrapFunctional(c.default || c))
export const LazyTimePickerItem = import('../..\\components\\TimePickerItem.vue' /* webpackChunkName: "components/time-picker-item" */).then(c => wrapFunctional(c.default || c))
export const LazyUserStatus = import('../..\\components\\UserStatus.vue' /* webpackChunkName: "components/user-status" */).then(c => wrapFunctional(c.default || c))
export const LazyYoutube = import('../..\\components\\Youtube.vue' /* webpackChunkName: "components/youtube" */).then(c => wrapFunctional(c.default || c))
export const LazyBusinessPage = import('../..\\components\\business-page\\BusinessPage.vue' /* webpackChunkName: "components/business-page" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomAudioItem = import('../..\\components\\classroom\\AudioItem.vue' /* webpackChunkName: "components/classroom-audio-item" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomContainer = import('../..\\components\\classroom\\ClassroomContainer.vue' /* webpackChunkName: "components/classroom-container" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomContainerHeader = import('../..\\components\\classroom\\ClassroomContainerHeader.vue' /* webpackChunkName: "components/classroom-container-header" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomDropFileArea = import('../..\\components\\classroom\\DropFileArea.vue' /* webpackChunkName: "components/classroom-drop-file-area" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomImageItem = import('../..\\components\\classroom\\ImageItem.vue' /* webpackChunkName: "components/classroom-image-item" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomKonva = import('../..\\components\\classroom\\Konva.vue' /* webpackChunkName: "components/classroom-konva" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomLibrary = import('../..\\components\\classroom\\Library.vue' /* webpackChunkName: "components/classroom-library" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomOtherCursor = import('../..\\components\\classroom\\OtherCursor.vue' /* webpackChunkName: "components/classroom-other-cursor" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomPdfItem = import('../..\\components\\classroom\\PdfItem.vue' /* webpackChunkName: "components/classroom-pdf-item" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomTinymceVue = import('../..\\components\\classroom\\TinymceVue.vue' /* webpackChunkName: "components/classroom-tinymce-vue" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomToolbar = import('../..\\components\\classroom\\Toolbar.vue' /* webpackChunkName: "components/classroom-toolbar" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoInput = import('../..\\components\\classroom\\VideoInput.vue' /* webpackChunkName: "components/classroom-video-input" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoItem = import('../..\\components\\classroom\\VideoItem.vue' /* webpackChunkName: "components/classroom-video-item" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomViewport = import('../..\\components\\classroom\\Viewport.vue' /* webpackChunkName: "components/classroom-viewport" */).then(c => wrapFunctional(c.default || c))
export const LazyFormEditor = import('../..\\components\\form\\Editor.vue' /* webpackChunkName: "components/form-editor" */).then(c => wrapFunctional(c.default || c))
export const LazyFormRate = import('../..\\components\\form\\FormRate.vue' /* webpackChunkName: "components/form-rate" */).then(c => wrapFunctional(c.default || c))
export const LazyFormSearchInput = import('../..\\components\\form\\SearchInput.vue' /* webpackChunkName: "components/form-search-input" */).then(c => wrapFunctional(c.default || c))
export const LazyFormSelectInput = import('../..\\components\\form\\SelectInput.vue' /* webpackChunkName: "components/form-select-input" */).then(c => wrapFunctional(c.default || c))
export const LazyFormSelectInputNew = import('../..\\components\\form\\SelectInputNew.vue' /* webpackChunkName: "components/form-select-input-new" */).then(c => wrapFunctional(c.default || c))
export const LazyFormTextInput = import('../..\\components\\form\\TextInput.vue' /* webpackChunkName: "components/form-text-input" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageAboutSection = import('../..\\components\\homepage\\AboutSection.vue' /* webpackChunkName: "components/homepage-about-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageFaqSection = import('../..\\components\\homepage\\FaqSection.vue' /* webpackChunkName: "components/homepage-faq-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageHowWorksSection = import('../..\\components\\homepage\\HowWorksSection.vue' /* webpackChunkName: "components/homepage-how-works-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageIntroSection = import('../..\\components\\homepage\\IntroSection.vue' /* webpackChunkName: "components/homepage-intro-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageLanguagesSection = import('../..\\components\\homepage\\LanguagesSection.vue' /* webpackChunkName: "components/homepage-languages-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageReviewSection = import('../..\\components\\homepage\\ReviewSection.vue' /* webpackChunkName: "components/homepage-review-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageSelectLanguage = import('../..\\components\\homepage\\SelectLanguage.vue' /* webpackChunkName: "components/homepage-select-language" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageStatSection = import('../..\\components\\homepage\\StatSection.vue' /* webpackChunkName: "components/homepage-stat-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageThinkingSection = import('../..\\components\\homepage\\ThinkingSection.vue' /* webpackChunkName: "components/homepage-thinking-section" */).then(c => wrapFunctional(c.default || c))
export const LazyHomepageTutorsSection = import('../..\\components\\homepage\\TutorsSection.vue' /* webpackChunkName: "components/homepage-tutors-section" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesAlarmGradientIcon = import('../..\\components\\images\\AlarmGradientIcon.vue' /* webpackChunkName: "components/images-alarm-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesBusinessPageIntroImage = import('../..\\components\\images\\BusinessPageIntroImage.vue' /* webpackChunkName: "components/images-business-page-intro-image" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesBusinessPageIntroMobileImage = import('../..\\components\\images\\BusinessPageIntroMobileImage.vue' /* webpackChunkName: "components/images-business-page-intro-mobile-image" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesCareerGradientIcon = import('../..\\components\\images\\CareerGradientIcon.vue' /* webpackChunkName: "components/images-career-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesCheckedGradientIcon = import('../..\\components\\images\\CheckedGradientIcon.vue' /* webpackChunkName: "components/images-checked-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesEducationGradientIcon = import('../..\\components\\images\\EducationGradientIcon.vue' /* webpackChunkName: "components/images-education-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesEmailIcon = import('../..\\components\\images\\EmailIcon.vue' /* webpackChunkName: "components/images-email-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesEnFlagIcon = import('../..\\components\\images\\EnFlagIcon.vue' /* webpackChunkName: "components/images-en-flag-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesEsFlagIcon = import('../..\\components\\images\\EsFlagIcon.vue' /* webpackChunkName: "components/images-es-flag-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesGoogleIcon = import('../..\\components\\images\\GoogleIcon.vue' /* webpackChunkName: "components/images-google-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesHomePageIntroImage = import('../..\\components\\images\\HomePageIntroImage.vue' /* webpackChunkName: "components/images-home-page-intro-image" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesLifeGradientIcon = import('../..\\components\\images\\LifeGradientIcon.vue' /* webpackChunkName: "components/images-life-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesMoonGradientIcon = import('../..\\components\\images\\MoonGradientIcon.vue' /* webpackChunkName: "components/images-moon-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesPlFlagIcon = import('../..\\components\\images\\PlFlagIcon.vue' /* webpackChunkName: "components/images-pl-flag-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesSearchIcon = import('../..\\components\\images\\SearchIcon.vue' /* webpackChunkName: "components/images-search-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesSunGradientIcon = import('../..\\components\\images\\SunGradientIcon.vue' /* webpackChunkName: "components/images-sun-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyImagesSunsetGradientIcon = import('../..\\components\\images\\SunsetGradientIcon.vue' /* webpackChunkName: "components/images-sunset-gradient-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyLandingPageTeachersSlider = import('../..\\components\\landing-page\\TeachersSlider.vue' /* webpackChunkName: "components/landing-page-teachers-slider" */).then(c => wrapFunctional(c.default || c))
export const LazyLandingPageTestimonialsSlider = import('../..\\components\\landing-page\\TestimonialsSlider.vue' /* webpackChunkName: "components/landing-page-testimonials-slider" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsCountries = import('../..\\components\\payments\\countries.js' /* webpackChunkName: "components/payments-countries" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPaymentDetailsModal = import('../..\\components\\payments\\PaymentDetailsModal.vue' /* webpackChunkName: "components/payments-payment-details-modal" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPaymentItem = import('../..\\components\\payments\\PaymentItem.vue' /* webpackChunkName: "components/payments-payment-item" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPaymentLesson = import('../..\\components\\payments\\PaymentLesson.vue' /* webpackChunkName: "components/payments-payment-lesson" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPaymentPayout = import('../..\\components\\payments\\PaymentPayout.vue' /* webpackChunkName: "components/payments-payment-payout" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPage = import('../..\\components\\payments\\PaymentsPage.vue' /* webpackChunkName: "components/payments-page" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPayoutItem = import('../..\\components\\payments\\PayoutItem.vue' /* webpackChunkName: "components/payments-payout-item" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsPayoutModal = import('../..\\components\\payments\\PayoutModal.vue' /* webpackChunkName: "components/payments-payout-modal" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsSavedAccountsModal = import('../..\\components\\payments\\SavedAccountsModal.vue' /* webpackChunkName: "components/payments-saved-accounts-modal" */).then(c => wrapFunctional(c.default || c))
export const LazyPaymentsWiseTransferModal = import('../..\\components\\payments\\WiseTransferModal.vue' /* webpackChunkName: "components/payments-wise-transfer-modal" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherListing = import('../..\\components\\teacher-listing\\TeacherListing.vue' /* webpackChunkName: "components/teacher-listing" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherListingBanner = import('../..\\components\\teacher-listing\\TeacherListingBanner.vue' /* webpackChunkName: "components/teacher-listing-banner" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherListingHeader = import('../..\\components\\teacher-listing\\TeacherListingHeader.vue' /* webpackChunkName: "components/teacher-listing-header" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfileCourseItem = import('../..\\components\\teacher-profile\\CourseItem.vue' /* webpackChunkName: "components/teacher-profile-course-item" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfileFeedbackTags = import('../..\\components\\teacher-profile\\FeedbackTags.vue' /* webpackChunkName: "components/teacher-profile-feedback-tags" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfileFindMoreTeachersButton = import('../..\\components\\teacher-profile\\FindMoreTeachersButton.vue' /* webpackChunkName: "components/teacher-profile-find-more-teachers-button" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfilePricePerLesson = import('../..\\components\\teacher-profile\\PricePerLesson.vue' /* webpackChunkName: "components/teacher-profile-price-per-lesson" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfileSidebar = import('../..\\components\\teacher-profile\\TeacherProfileSidebar.vue' /* webpackChunkName: "components/teacher-profile-sidebar" */).then(c => wrapFunctional(c.default || c))
export const LazyTeacherProfileTimePickerDialog = import('../..\\components\\teacher-profile\\TimePickerDialog.vue' /* webpackChunkName: "components/teacher-profile-time-picker-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsLessonEvaluationDialog = import('../..\\components\\user-lessons\\LessonEvaluationDialog.vue' /* webpackChunkName: "components/user-lessons-lesson-evaluation-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsLessonItem = import('../..\\components\\user-lessons\\LessonItem.vue' /* webpackChunkName: "components/user-lessons-lesson-item" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsPage = import('../..\\components\\user-lessons\\LessonsPage.vue' /* webpackChunkName: "components/user-lessons-page" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsPastLesson = import('../..\\components\\user-lessons\\PastLesson.vue' /* webpackChunkName: "components/user-lessons-past-lesson" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsTimePickerDialog = import('../..\\components\\user-lessons\\TimePickerDialog.vue' /* webpackChunkName: "components/user-lessons-time-picker-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsUnscheduledLesson = import('../..\\components\\user-lessons\\UnscheduledLesson.vue' /* webpackChunkName: "components/user-lessons-unscheduled-lesson" */).then(c => wrapFunctional(c.default || c))
export const LazyUserLessonsUpcomingLesson = import('../..\\components\\user-lessons\\UpcomingLesson.vue' /* webpackChunkName: "components/user-lessons-upcoming-lesson" */).then(c => wrapFunctional(c.default || c))
export const LazyUserMessagesConversation = import('../..\\components\\user-messages\\Conversation.vue' /* webpackChunkName: "components/user-messages-conversation" */).then(c => wrapFunctional(c.default || c))
export const LazyUserMessagesConversationItem = import('../..\\components\\user-messages\\ConversationItem.vue' /* webpackChunkName: "components/user-messages-conversation-item" */).then(c => wrapFunctional(c.default || c))
export const LazyUserMessagesEmptyContent = import('../..\\components\\user-messages\\EmptyContent.vue' /* webpackChunkName: "components/user-messages-empty-content" */).then(c => wrapFunctional(c.default || c))
export const LazyUserMessagesPage = import('../..\\components\\user-messages\\MessagesPage.vue' /* webpackChunkName: "components/user-messages-page" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsAboutMeInfo = import('../..\\components\\user-settings\\AboutMeInfo.vue' /* webpackChunkName: "components/user-settings-about-me-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsAddQualificationDialog = import('../..\\components\\user-settings\\AddQualificationDialog.vue' /* webpackChunkName: "components/user-settings-add-qualification-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsBackgroundInfo = import('../..\\components\\user-settings\\BackgroundInfo.vue' /* webpackChunkName: "components/user-settings-background-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsBasicInfo = import('../..\\components\\user-settings\\BasicInfo.vue' /* webpackChunkName: "components/user-settings-basic-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsCalendarNotificationInfo = import('../..\\components\\user-settings\\CalendarNotificationInfo.vue' /* webpackChunkName: "components/user-settings-calendar-notification-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsCourseItem = import('../..\\components\\user-settings\\CourseItem.vue' /* webpackChunkName: "components/user-settings-course-item" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsCoursesInfo = import('../..\\components\\user-settings\\CoursesInfo.vue' /* webpackChunkName: "components/user-settings-courses-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsIllustrationDialog = import('../..\\components\\user-settings\\IllustrationDialog.vue' /* webpackChunkName: "components/user-settings-illustration-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsLanguagesInfo = import('../..\\components\\user-settings\\LanguagesInfo.vue' /* webpackChunkName: "components/user-settings-languages-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsLearningPreferencesInfo = import('../..\\components\\user-settings\\LearningPreferencesInfo.vue' /* webpackChunkName: "components/user-settings-learning-preferences-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsLessonPrice = import('../..\\components\\user-settings\\LessonPrice.vue' /* webpackChunkName: "components/user-settings-lesson-price" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsPerLessonPrice = import('../..\\components\\user-settings\\PerLessonPrice.vue' /* webpackChunkName: "components/user-settings-per-lesson-price" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsPricingTableInfo = import('../..\\components\\user-settings\\PricingTableInfo.vue' /* webpackChunkName: "components/user-settings-pricing-table-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsQualificationSuccessDialog = import('../..\\components\\user-settings\\QualificationSuccessDialog.vue' /* webpackChunkName: "components/user-settings-qualification-success-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsReceiptInfo = import('../..\\components\\user-settings\\ReceiptInfo.vue' /* webpackChunkName: "components/user-settings-receipt-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsSpecialityDialog = import('../..\\components\\user-settings\\SpecialityDialog.vue' /* webpackChunkName: "components/user-settings-speciality-dialog" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsSummaryInfo = import('../..\\components\\user-settings\\SummaryInfo.vue' /* webpackChunkName: "components/user-settings-summary-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsTeachingPreferencesInfo = import('../..\\components\\user-settings\\TeachingPreferencesInfo.vue' /* webpackChunkName: "components/user-settings-teaching-preferences-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingsTeachingQualificationsInfo = import('../..\\components\\user-settings\\TeachingQualificationsInfo.vue' /* webpackChunkName: "components/user-settings-teaching-qualifications-info" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingAutocomplete = import('../..\\components\\user-settings\\UserSettingAutocomplete.vue' /* webpackChunkName: "components/user-setting-autocomplete" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingSelect = import('../..\\components\\user-settings\\UserSettingSelect.vue' /* webpackChunkName: "components/user-setting-select" */).then(c => wrapFunctional(c.default || c))
export const LazyUserSettingTemplate = import('../..\\components\\user-settings\\UserSettingTemplate.vue' /* webpackChunkName: "components/user-setting-template" */).then(c => wrapFunctional(c.default || c))
export const LazyBusinessPageIconsDotsIcon = import('../..\\components\\business-page\\icons\\DotsIcon.vue' /* webpackChunkName: "components/business-page-icons-dots-icon" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoTokbox = import('../..\\components\\classroom\\video\\Tokbox.vue' /* webpackChunkName: "components/classroom-video-tokbox" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoTwilio = import('../..\\components\\classroom\\video\\Twilio.vue' /* webpackChunkName: "components/classroom-video-twilio" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoActions = import('../..\\components\\classroom\\video\\VideoActions.vue' /* webpackChunkName: "components/classroom-video-actions" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVideoWhereby = import('../..\\components\\classroom\\video\\Whereby.vue' /* webpackChunkName: "components/classroom-video-whereby" */).then(c => wrapFunctional(c.default || c))
export const LazyClassroomVueDraggableResizable = import('../..\\components\\classroom\\vue-draggable-resizable\\VueDraggableResizable.vue' /* webpackChunkName: "components/classroom-vue-draggable-resizable" */).then(c => wrapFunctional(c.default || c))
