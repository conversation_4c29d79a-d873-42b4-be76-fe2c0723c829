import dayjs from 'dayjs'

import 'dayjs/locale/en'
import 'dayjs/locale/pl'
import 'dayjs/locale/es'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isBetween from 'dayjs/plugin/isBetween'
import isoWeek from 'dayjs/plugin/isoWeek'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import localizedFormat from 'dayjs/plugin/localizedFormat'

dayjs.extend(isSameOrAfter)
dayjs.extend(isBetween)
dayjs.extend(isoWeek)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(advancedFormat)
dayjs.extend(localizedFormat)

export default (context, inject) => {
  context.$dayjs = dayjs
  inject('dayjs', dayjs)
}
