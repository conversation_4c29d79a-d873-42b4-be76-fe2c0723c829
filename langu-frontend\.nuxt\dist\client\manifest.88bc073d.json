{"name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "icons": [{"src": "/_nuxt/icons/icon_64x64.12fd45.png", "sizes": "64x64", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_120x120.12fd45.png", "sizes": "120x120", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_144x144.12fd45.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_152x152.12fd45.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_192x192.12fd45.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_384x384.12fd45.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/_nuxt/icons/icon_512x512.12fd45.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "start_url": "/?standalone=true", "display": "standalone", "background_color": "#f8faff", "theme_color": "#80b622", "lang": "en", "prefer_related_applications": true, "orientation": "portrait"}