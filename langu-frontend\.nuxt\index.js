import Vue from 'vue'
import Vuex from 'vuex'
import Meta from 'vue-meta'
import ClientOnly from 'vue-client-only'
import NoSsr from 'vue-no-ssr'
import { createRouter } from './router.js'
import NuxtChild from './components/nuxt-child.js'
import NuxtError from '..\\layouts\\error.vue'
import Nuxt from './components/nuxt.js'
import App from './App.js'
import { setContext, getLocation, getRouteData, normalizeError } from './utils'
import { createStore } from './store.js'

/* Plugins */

import nuxt_plugin_plugin_918ce648 from 'nuxt_plugin_plugin_918ce648' // Source: .\\components\\plugin.js (mode: 'all')
import nuxt_plugin_sentryserver_532abbc3 from 'nuxt_plugin_sentryserver_532abbc3' // Source: .\\sentry.server.js (mode: 'server')
import nuxt_plugin_sentryclient_251d6b8a from 'nuxt_plugin_sentryclient_251d6b8a' // Source: .\\sentry.client.js (mode: 'client')
import nuxt_plugin_plugin_69f8bc98 from 'nuxt_plugin_plugin_69f8bc98' // Source: .\\vuetify\\plugin.js (mode: 'all')
import nuxt_plugin_dayjsplugin_84763e04 from 'nuxt_plugin_dayjsplugin_84763e04' // Source: .\\dayjs-plugin.js (mode: 'all')
import nuxt_plugin_cookieuniversalnuxt_66b22ca7 from 'nuxt_plugin_cookieuniversalnuxt_66b22ca7' // Source: .\\cookie-universal-nuxt.js (mode: 'all')
import nuxt_plugin_pluginutils_1fc3814f from 'nuxt_plugin_pluginutils_1fc3814f' // Source: .\\nuxt-i18n\\plugin.utils.js (mode: 'all')
import nuxt_plugin_pluginrouting_1a2e124c from 'nuxt_plugin_pluginrouting_1a2e124c' // Source: .\\nuxt-i18n\\plugin.routing.js (mode: 'all')
import nuxt_plugin_pluginmain_e8061a56 from 'nuxt_plugin_pluginmain_e8061a56' // Source: .\\nuxt-i18n\\plugin.main.js (mode: 'all')
import nuxt_plugin_axios_72986aff from 'nuxt_plugin_axios_72986aff' // Source: .\\axios.js (mode: 'all')
import nuxt_plugin_workbox_a00bc74a from 'nuxt_plugin_workbox_a00bc74a' // Source: .\\workbox.js (mode: 'client')
import nuxt_plugin_metaplugin_5946f44a from 'nuxt_plugin_metaplugin_5946f44a' // Source: .\\pwa\\meta.plugin.js (mode: 'all')
import nuxt_plugin_iconplugin_4ff7a962 from 'nuxt_plugin_iconplugin_4ff7a962' // Source: .\\pwa\\icon.plugin.js (mode: 'all')
import nuxt_plugin_axios_2228ef02 from 'nuxt_plugin_axios_2228ef02' // Source: ..\\plugins\\axios (mode: 'all')
import nuxt_plugin_pluralization_2cc9f228 from 'nuxt_plugin_pluralization_2cc9f228' // Source: ..\\plugins\\pluralization (mode: 'all')
import nuxt_plugin_tidiochat_0e0dd5b8 from 'nuxt_plugin_tidiochat_0e0dd5b8' // Source: ..\\plugins\\tidio-chat (mode: 'client')
import nuxt_plugin_socket_412a8e67 from 'nuxt_plugin_socket_412a8e67' // Source: ..\\plugins\\socket (mode: 'client')
import nuxt_plugin_konva_22b1d641 from 'nuxt_plugin_konva_22b1d641' // Source: ..\\plugins\\konva (mode: 'client')
import nuxt_plugin_vuerating_42a0f690 from 'nuxt_plugin_vuerating_42a0f690' // Source: ..\\plugins\\vue-rating (mode: 'client')
import nuxt_plugin_stripe_4177cf41 from 'nuxt_plugin_stripe_4177cf41' // Source: ..\\plugins\\stripe (mode: 'client')
import nuxt_plugin_gtm_caeab2a8 from 'nuxt_plugin_gtm_caeab2a8' // Source: ..\\plugins\\gtm (mode: 'client')
import nuxt_plugin_sentryclient_6c961cf1 from 'nuxt_plugin_sentryclient_6c961cf1' // Source: ..\\plugins\\sentry.client.js (mode: 'client')
import nuxt_plugin_router_aacea2cc from 'nuxt_plugin_router_aacea2cc' // Source: ..\\plugins\\router.js (mode: 'all')

// Component: <ClientOnly>
Vue.component(ClientOnly.name, ClientOnly)

// TODO: Remove in Nuxt 3: <NoSsr>
Vue.component(NoSsr.name, {
  ...NoSsr,
  render (h, ctx) {
    if (process.client && !NoSsr._warned) {
      NoSsr._warned = true

      console.warn('<no-ssr> has been deprecated and will be removed in Nuxt 3, please use <client-only> instead')
    }
    return NoSsr.render(h, ctx)
  }
})

// Component: <NuxtChild>
Vue.component(NuxtChild.name, NuxtChild)
Vue.component('NChild', NuxtChild)

// Component NuxtLink is imported in server.js or client.js

// Component: <Nuxt>
Vue.component(Nuxt.name, Nuxt)

Object.defineProperty(Vue.prototype, '$nuxt', {
  get() {
    const globalNuxt = this.$root.$options.$nuxt
    if (process.client && !globalNuxt && typeof window !== 'undefined') {
      return window.$nuxt
    }
    return globalNuxt
  },
  configurable: true
})

Vue.use(Meta, {"keyName":"head","attribute":"data-n-head","ssrAttribute":"data-n-head-ssr","tagIDKeyName":"hid"})

const defaultTransition = {"name":"page","mode":"out-in","appear":false,"appearClass":"appear","appearActiveClass":"appear-active","appearToClass":"appear-to"}

const originalRegisterModule = Vuex.Store.prototype.registerModule

function registerModule (path, rawModule, options = {}) {
  const preserveState = process.client && (
    Array.isArray(path)
      ? !!path.reduce((namespacedState, path) => namespacedState && namespacedState[path], this.state)
      : path in this.state
  )
  return originalRegisterModule.call(this, path, rawModule, { preserveState, ...options })
}

async function createApp(ssrContext, config = {}) {
  const router = await createRouter(ssrContext, config)

  const store = createStore(ssrContext)
  // Add this.$router into store actions/mutations
  store.$router = router

  // Fix SSR caveat https://github.com/nuxt/nuxt.js/issues/3757#issuecomment-414689141
  store.registerModule = registerModule

  // Create Root instance

  // here we inject the router and store to all child components,
  // making them available everywhere as `this.$router` and `this.$store`.
  const app = {
    head: {"titleTemplate":function anonymous(titleChunk
) {
return titleChunk || 'Langu'
},"meta":[{"charset":"utf-8"},{"name":"viewport","content":"width=device-width, initial-scale=1"},{"hid":"description","name":"description","content":undefined}],"link":[{"rel":"icon","type":"image\u002Fx-icon","href":"\u002Fimages\u002Ffavicon.ico"},{"rel":"preconnect","href":"https:\u002F\u002Ffonts.googleapis.com"},{"rel":"preconnect","href":"https:\u002F\u002Ffonts.gstatic.com","crossorigin":true},{"rel":"stylesheet","href":"@\u002Fassets\u002Fstyles\u002F_fonts.scss"}],"script":[{"innerHTML":"\n          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n          'https:\u002F\u002Fwww.googletagmanager.com\u002Fgtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n          })(window,document,'script','dataLayer','GTM-WB9P3RB');\n        ","type":"text\u002Fjavascript","charset":"utf-8"}],"__dangerouslyDisableSanitizers":["script"],"style":[]},

    store,
    router,
    nuxt: {
      defaultTransition,
      transitions: [defaultTransition],
      setTransitions (transitions) {
        if (!Array.isArray(transitions)) {
          transitions = [transitions]
        }
        transitions = transitions.map((transition) => {
          if (!transition) {
            transition = defaultTransition
          } else if (typeof transition === 'string') {
            transition = Object.assign({}, defaultTransition, { name: transition })
          } else {
            transition = Object.assign({}, defaultTransition, transition)
          }
          return transition
        })
        this.$options.nuxt.transitions = transitions
        return transitions
      },

      err: null,
      dateErr: null,
      error (err) {
        err = err || null
        app.context._errored = Boolean(err)
        err = err ? normalizeError(err) : null
        let nuxt = app.nuxt // to work with @vue/composition-api, see https://github.com/nuxt/nuxt.js/issues/6517#issuecomment-573280207
        if (this) {
          nuxt = this.nuxt || this.$options.nuxt
        }
        nuxt.dateErr = Date.now()
        nuxt.err = err
        // Used in src/server.js
        if (ssrContext) {
          ssrContext.nuxt.error = err
        }
        return err
      }
    },
    ...App
  }

  // Make app available into store via this.app
  store.app = app

  const next = ssrContext ? ssrContext.next : location => app.router.push(location)
  // Resolve route
  let route
  if (ssrContext) {
    route = router.resolve(ssrContext.url).route
  } else {
    const path = getLocation(router.options.base, router.options.mode)
    route = router.resolve(path).route
  }

  // Set context to app.context
  await setContext(app, {
    store,
    route,
    next,
    error: app.nuxt.error.bind(app),
    payload: ssrContext ? ssrContext.payload : undefined,
    req: ssrContext ? ssrContext.req : undefined,
    res: ssrContext ? ssrContext.res : undefined,
    beforeRenderFns: ssrContext ? ssrContext.beforeRenderFns : undefined,
    ssrContext
  })

  function inject(key, value) {
    if (!key) {
      throw new Error('inject(key, value) has no key provided')
    }
    if (value === undefined) {
      throw new Error(`inject('${key}', value) has no value provided`)
    }

    key = '$' + key
    // Add into app
    app[key] = value
    // Add into context
    if (!app.context[key]) {
      app.context[key] = value
    }

    // Add into store
    store[key] = app[key]

    // Check if plugin not already installed
    const installKey = '__nuxt_' + key + '_installed__'
    if (Vue[installKey]) {
      return
    }
    Vue[installKey] = true
    // Call Vue.use() to install the plugin into vm
    Vue.use(() => {
      if (!Object.prototype.hasOwnProperty.call(Vue.prototype, key)) {
        Object.defineProperty(Vue.prototype, key, {
          get () {
            return this.$root.$options[key]
          }
        })
      }
    })
  }

  // Inject runtime config as $config
  inject('config', config)

  if (process.client) {
    // Replace store state before plugins execution
    if (window.__NUXT__ && window.__NUXT__.state) {
      store.replaceState(window.__NUXT__.state)
    }
  }

  // Add enablePreview(previewData = {}) in context for plugins
  if (process.static && process.client) {
    app.context.enablePreview = function (previewData = {}) {
      app.previewData = Object.assign({}, previewData)
      inject('preview', previewData)
    }
  }
  // Plugin execution

  if (typeof nuxt_plugin_plugin_918ce648 === 'function') {
    await nuxt_plugin_plugin_918ce648(app.context, inject)
  }

  if (process.server && typeof nuxt_plugin_sentryserver_532abbc3 === 'function') {
    await nuxt_plugin_sentryserver_532abbc3(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_sentryclient_251d6b8a === 'function') {
    await nuxt_plugin_sentryclient_251d6b8a(app.context, inject)
  }

  if (typeof nuxt_plugin_plugin_69f8bc98 === 'function') {
    await nuxt_plugin_plugin_69f8bc98(app.context, inject)
  }

  if (typeof nuxt_plugin_dayjsplugin_84763e04 === 'function') {
    await nuxt_plugin_dayjsplugin_84763e04(app.context, inject)
  }

  if (typeof nuxt_plugin_cookieuniversalnuxt_66b22ca7 === 'function') {
    await nuxt_plugin_cookieuniversalnuxt_66b22ca7(app.context, inject)
  }

  if (typeof nuxt_plugin_pluginutils_1fc3814f === 'function') {
    await nuxt_plugin_pluginutils_1fc3814f(app.context, inject)
  }

  if (typeof nuxt_plugin_pluginrouting_1a2e124c === 'function') {
    await nuxt_plugin_pluginrouting_1a2e124c(app.context, inject)
  }

  if (typeof nuxt_plugin_pluginmain_e8061a56 === 'function') {
    await nuxt_plugin_pluginmain_e8061a56(app.context, inject)
  }

  if (typeof nuxt_plugin_axios_72986aff === 'function') {
    await nuxt_plugin_axios_72986aff(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_workbox_a00bc74a === 'function') {
    await nuxt_plugin_workbox_a00bc74a(app.context, inject)
  }

  if (typeof nuxt_plugin_metaplugin_5946f44a === 'function') {
    await nuxt_plugin_metaplugin_5946f44a(app.context, inject)
  }

  if (typeof nuxt_plugin_iconplugin_4ff7a962 === 'function') {
    await nuxt_plugin_iconplugin_4ff7a962(app.context, inject)
  }

  if (typeof nuxt_plugin_axios_2228ef02 === 'function') {
    await nuxt_plugin_axios_2228ef02(app.context, inject)
  }

  if (typeof nuxt_plugin_pluralization_2cc9f228 === 'function') {
    await nuxt_plugin_pluralization_2cc9f228(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_tidiochat_0e0dd5b8 === 'function') {
    await nuxt_plugin_tidiochat_0e0dd5b8(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_socket_412a8e67 === 'function') {
    await nuxt_plugin_socket_412a8e67(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_konva_22b1d641 === 'function') {
    await nuxt_plugin_konva_22b1d641(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_vuerating_42a0f690 === 'function') {
    await nuxt_plugin_vuerating_42a0f690(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_stripe_4177cf41 === 'function') {
    await nuxt_plugin_stripe_4177cf41(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_gtm_caeab2a8 === 'function') {
    await nuxt_plugin_gtm_caeab2a8(app.context, inject)
  }

  if (process.client && typeof nuxt_plugin_sentryclient_6c961cf1 === 'function') {
    await nuxt_plugin_sentryclient_6c961cf1(app.context, inject)
  }

  if (typeof nuxt_plugin_router_aacea2cc === 'function') {
    await nuxt_plugin_router_aacea2cc(app.context, inject)
  }

  // Lock enablePreview in context
  if (process.static && process.client) {
    app.context.enablePreview = function () {
      console.warn('You cannot call enablePreview() outside a plugin.')
    }
  }

  // Wait for async component to be resolved first
  await new Promise((resolve, reject) => {
    router.push(app.context.route.fullPath, resolve, (err) => {
      // https://github.com/vuejs/vue-router/blob/v3.4.3/src/util/errors.js
      if (!err._isRouter) return reject(err)
      if (err.type !== 2 /* NavigationFailureType.redirected */) return resolve()

      // navigated to a different route in router guard
      const unregister = router.afterEach(async (to, from) => {
        if (process.server && ssrContext && ssrContext.url) {
          ssrContext.url = to.fullPath
        }
        app.context.route = await getRouteData(to)
        app.context.params = to.params || {}
        app.context.query = to.query || {}
        unregister()
        resolve()
      })
    })
  })

  return {
    store,
    app,
    router
  }
}

export { createApp, NuxtError }
