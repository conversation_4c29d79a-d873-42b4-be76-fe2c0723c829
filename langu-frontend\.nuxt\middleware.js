const middleware = {}

middleware['authenticated'] = require('..\\middleware\\authenticated.js')
middleware['authenticated'] = middleware['authenticated'].default || middleware['authenticated']

middleware['businessSuccessPageAllowed'] = require('..\\middleware\\businessSuccessPageAllowed.js')
middleware['businessSuccessPageAllowed'] = middleware['businessSuccessPageAllowed'].default || middleware['businessSuccessPageAllowed']

middleware['confirmationPageAllowed'] = require('..\\middleware\\confirmationPageAllowed.js')
middleware['confirmationPageAllowed'] = middleware['confirmationPageAllowed'].default || middleware['confirmationPageAllowed']

middleware['paymentsPageClass'] = require('..\\middleware\\paymentsPageClass.js')
middleware['paymentsPageClass'] = middleware['paymentsPageClass'].default || middleware['paymentsPageClass']

middleware['redirect'] = require('..\\middleware\\redirect.js')
middleware['redirect'] = middleware['redirect'].default || middleware['redirect']

middleware['teacherListingRedirect'] = require('..\\middleware\\teacherListingRedirect.js')
middleware['teacherListingRedirect'] = middleware['teacherListingRedirect'].default || middleware['teacherListingRedirect']

middleware['thankYouPageAllowed'] = require('..\\middleware\\thankYouPageAllowed.js')
middleware['thankYouPageAllowed'] = middleware['thankYouPageAllowed'].default || middleware['thankYouPageAllowed']

middleware['threadExisted'] = require('..\\middleware\\threadExisted.js')
middleware['threadExisted'] = middleware['threadExisted'].default || middleware['threadExisted']

middleware['utm'] = require('..\\middleware\\utm.js')
middleware['utm'] = middleware['utm'].default || middleware['utm']

export default middleware
