{"BuzzDialog": {"description": "Auto imported from components/BuzzDialog.vue"}, "Calendar": {"description": "Auto imported from components/Calendar.vue"}, "CalendarDate": {"description": "Auto imported from components/CalendarDate.vue"}, "CheckEmailDialog": {"description": "Auto imported from components/CheckEmailDialog.vue"}, "ConfirmDialog": {"description": "Auto imported from components/ConfirmDialog.vue"}, "CookiePopup": {"description": "Auto imported from components/CookiePopup.vue"}, "Footer": {"description": "Auto imported from components/Footer.vue"}, "FreeSlots": {"description": "Auto imported from components/FreeSlots.vue"}, "GoogleSignInButton": {"description": "Auto imported from components/GoogleSignInButton.vue"}, "Header": {"description": "Auto imported from components/Header.vue"}, "LAvatar": {"description": "Auto imported from components/LAvatar.vue"}, "LChip": {"description": "Auto imported from components/LChip.vue"}, "LDialog": {"description": "Auto imported from components/LDialog.vue"}, "LessonTimeNotice": {"description": "Auto imported from components/LessonTimeNotice.vue"}, "LExpansionPanels": {"description": "Auto imported from components/LExpansionPanels.vue"}, "Loader": {"description": "Auto imported from components/Loader.vue"}, "LoadMoreBtn": {"description": "Auto imported from components/LoadMoreBtn.vue"}, "LoginSidebar": {"description": "Auto imported from components/LoginSidebar.vue"}, "MessageDialog": {"description": "Auto imported from components/MessageDialog.vue"}, "Pagination": {"description": "Auto imported from components/Pagination.vue"}, "SetPasswordDialog": {"description": "Auto imported from components/SetPasswordDialog.vue"}, "Snackbar": {"description": "Auto imported from components/Snackbar.vue"}, "StarRating": {"description": "Auto imported from components/StarRating.vue"}, "Steps": {"description": "Auto imported from components/Steps.vue"}, "SummaryLessonDialog": {"description": "Auto imported from components/SummaryLessonDialog.vue"}, "TeacherCard": {"description": "Auto imported from components/TeacherCard.vue"}, "TeacherFilter": {"description": "Auto imported from components/TeacherFilter.vue"}, "TeacherFilterNew": {"description": "Auto imported from components/TeacherFilterNew.vue"}, "TimePicker": {"description": "Auto imported from components/TimePicker.vue"}, "TimePickerItem": {"description": "Auto imported from components/TimePickerItem.vue"}, "UserStatus": {"description": "Auto imported from components/UserStatus.vue"}, "Youtube": {"description": "Auto imported from components/Youtube.vue"}, "BusinessPage": {"description": "Auto imported from components/business-page/BusinessPage.vue"}, "ClassroomAudioItem": {"description": "Auto imported from components/classroom/AudioItem.vue"}, "ClassroomContainer": {"description": "Auto imported from components/classroom/ClassroomContainer.vue"}, "ClassroomContainerHeader": {"description": "Auto imported from components/classroom/ClassroomContainerHeader.vue"}, "ClassroomDropFileArea": {"description": "Auto imported from components/classroom/DropFileArea.vue"}, "ClassroomImageItem": {"description": "Auto imported from components/classroom/ImageItem.vue"}, "ClassroomKonva": {"description": "Auto imported from components/classroom/Konva.vue"}, "ClassroomLibrary": {"description": "Auto imported from components/classroom/Library.vue"}, "ClassroomOtherCursor": {"description": "Auto imported from components/classroom/OtherCursor.vue"}, "ClassroomPdfItem": {"description": "Auto imported from components/classroom/PdfItem.vue"}, "ClassroomTinymceVue": {"description": "Auto imported from components/classroom/TinymceVue.vue"}, "ClassroomToolbar": {"description": "Auto imported from components/classroom/Toolbar.vue"}, "ClassroomVideoInput": {"description": "Auto imported from components/classroom/VideoInput.vue"}, "ClassroomVideoItem": {"description": "Auto imported from components/classroom/VideoItem.vue"}, "ClassroomViewport": {"description": "Auto imported from components/classroom/Viewport.vue"}, "FormEditor": {"description": "Auto imported from components/form/Editor.vue"}, "FormRate": {"description": "Auto imported from components/form/FormRate.vue"}, "FormSearchInput": {"description": "Auto imported from components/form/SearchInput.vue"}, "FormSelectInput": {"description": "Auto imported from components/form/SelectInput.vue"}, "FormSelectInputNew": {"description": "Auto imported from components/form/SelectInputNew.vue"}, "FormTextInput": {"description": "Auto imported from components/form/TextInput.vue"}, "HomepageAboutSection": {"description": "Auto imported from components/homepage/AboutSection.vue"}, "HomepageFaqSection": {"description": "Auto imported from components/homepage/FaqSection.vue"}, "HomepageHowWorksSection": {"description": "Auto imported from components/homepage/HowWorksSection.vue"}, "HomepageIntroSection": {"description": "Auto imported from components/homepage/IntroSection.vue"}, "HomepageLanguagesSection": {"description": "Auto imported from components/homepage/LanguagesSection.vue"}, "HomepageReviewSection": {"description": "Auto imported from components/homepage/ReviewSection.vue"}, "HomepageSelectLanguage": {"description": "Auto imported from components/homepage/SelectLanguage.vue"}, "HomepageStatSection": {"description": "Auto imported from components/homepage/StatSection.vue"}, "HomepageThinkingSection": {"description": "Auto imported from components/homepage/ThinkingSection.vue"}, "HomepageTutorsSection": {"description": "Auto imported from components/homepage/TutorsSection.vue"}, "ImagesAlarmGradientIcon": {"description": "Auto imported from components/images/AlarmGradientIcon.vue"}, "ImagesBusinessPageIntroImage": {"description": "Auto imported from components/images/BusinessPageIntroImage.vue"}, "ImagesBusinessPageIntroMobileImage": {"description": "Auto imported from components/images/BusinessPageIntroMobileImage.vue"}, "ImagesCareerGradientIcon": {"description": "Auto imported from components/images/CareerGradientIcon.vue"}, "ImagesCheckedGradientIcon": {"description": "Auto imported from components/images/CheckedGradientIcon.vue"}, "ImagesEducationGradientIcon": {"description": "Auto imported from components/images/EducationGradientIcon.vue"}, "ImagesEmailIcon": {"description": "Auto imported from components/images/EmailIcon.vue"}, "ImagesEnFlagIcon": {"description": "Auto imported from components/images/EnFlagIcon.vue"}, "ImagesEsFlagIcon": {"description": "Auto imported from components/images/EsFlagIcon.vue"}, "ImagesGoogleIcon": {"description": "Auto imported from components/images/GoogleIcon.vue"}, "ImagesHomePageIntroImage": {"description": "Auto imported from components/images/HomePageIntroImage.vue"}, "ImagesLifeGradientIcon": {"description": "Auto imported from components/images/LifeGradientIcon.vue"}, "ImagesMoonGradientIcon": {"description": "Auto imported from components/images/MoonGradientIcon.vue"}, "ImagesPlFlagIcon": {"description": "Auto imported from components/images/PlFlagIcon.vue"}, "ImagesSearchIcon": {"description": "Auto imported from components/images/SearchIcon.vue"}, "ImagesSunGradientIcon": {"description": "Auto imported from components/images/SunGradientIcon.vue"}, "ImagesSunsetGradientIcon": {"description": "Auto imported from components/images/SunsetGradientIcon.vue"}, "LandingPageTeachersSlider": {"description": "Auto imported from components/landing-page/TeachersSlider.vue"}, "LandingPageTestimonialsSlider": {"description": "Auto imported from components/landing-page/TestimonialsSlider.vue"}, "PaymentsCountries": {"description": "Auto imported from components/payments/countries.js"}, "PaymentsPaymentDetailsModal": {"description": "Auto imported from components/payments/PaymentDetailsModal.vue"}, "PaymentsPaymentItem": {"description": "Auto imported from components/payments/PaymentItem.vue"}, "PaymentsPaymentLesson": {"description": "Auto imported from components/payments/PaymentLesson.vue"}, "PaymentsPaymentPayout": {"description": "Auto imported from components/payments/PaymentPayout.vue"}, "PaymentsPage": {"description": "Auto imported from components/payments/PaymentsPage.vue"}, "PaymentsPayoutItem": {"description": "Auto imported from components/payments/PayoutItem.vue"}, "PaymentsPayoutModal": {"description": "Auto imported from components/payments/PayoutModal.vue"}, "PaymentsSavedAccountsModal": {"description": "Auto imported from components/payments/SavedAccountsModal.vue"}, "PaymentsWiseTransferModal": {"description": "Auto imported from components/payments/WiseTransferModal.vue"}, "TeacherListing": {"description": "Auto imported from components/teacher-listing/TeacherListing.vue"}, "TeacherListingBanner": {"description": "Auto imported from components/teacher-listing/TeacherListingBanner.vue"}, "TeacherListingHeader": {"description": "Auto imported from components/teacher-listing/TeacherListingHeader.vue"}, "TeacherProfileCourseItem": {"description": "Auto imported from components/teacher-profile/CourseItem.vue"}, "TeacherProfileFeedbackTags": {"description": "Auto imported from components/teacher-profile/FeedbackTags.vue"}, "TeacherProfileFindMoreTeachersButton": {"description": "Auto imported from components/teacher-profile/FindMoreTeachersButton.vue"}, "TeacherProfilePricePerLesson": {"description": "Auto imported from components/teacher-profile/PricePerLesson.vue"}, "TeacherProfileSidebar": {"description": "Auto imported from components/teacher-profile/TeacherProfileSidebar.vue"}, "TeacherProfileTimePickerDialog": {"description": "Auto imported from components/teacher-profile/TimePickerDialog.vue"}, "UserLessonsLessonEvaluationDialog": {"description": "Auto imported from components/user-lessons/LessonEvaluationDialog.vue"}, "UserLessonsLessonItem": {"description": "Auto imported from components/user-lessons/LessonItem.vue"}, "UserLessonsPage": {"description": "Auto imported from components/user-lessons/LessonsPage.vue"}, "UserLessonsPastLesson": {"description": "Auto imported from components/user-lessons/PastLesson.vue"}, "UserLessonsTimePickerDialog": {"description": "Auto imported from components/user-lessons/TimePickerDialog.vue"}, "UserLessonsUnscheduledLesson": {"description": "Auto imported from components/user-lessons/UnscheduledLesson.vue"}, "UserLessonsUpcomingLesson": {"description": "Auto imported from components/user-lessons/UpcomingLesson.vue"}, "UserMessagesConversation": {"description": "Auto imported from components/user-messages/Conversation.vue"}, "UserMessagesConversationItem": {"description": "Auto imported from components/user-messages/ConversationItem.vue"}, "UserMessagesEmptyContent": {"description": "Auto imported from components/user-messages/EmptyContent.vue"}, "UserMessagesPage": {"description": "Auto imported from components/user-messages/MessagesPage.vue"}, "UserSettingsAboutMeInfo": {"description": "Auto imported from components/user-settings/AboutMeInfo.vue"}, "UserSettingsAddQualificationDialog": {"description": "Auto imported from components/user-settings/AddQualificationDialog.vue"}, "UserSettingsBackgroundInfo": {"description": "Auto imported from components/user-settings/BackgroundInfo.vue"}, "UserSettingsBasicInfo": {"description": "Auto imported from components/user-settings/BasicInfo.vue"}, "UserSettingsCalendarNotificationInfo": {"description": "Auto imported from components/user-settings/CalendarNotificationInfo.vue"}, "UserSettingsCourseItem": {"description": "Auto imported from components/user-settings/CourseItem.vue"}, "UserSettingsCoursesInfo": {"description": "Auto imported from components/user-settings/CoursesInfo.vue"}, "UserSettingsIllustrationDialog": {"description": "Auto imported from components/user-settings/IllustrationDialog.vue"}, "UserSettingsLanguagesInfo": {"description": "Auto imported from components/user-settings/LanguagesInfo.vue"}, "UserSettingsLearningPreferencesInfo": {"description": "Auto imported from components/user-settings/LearningPreferencesInfo.vue"}, "UserSettingsLessonPrice": {"description": "Auto imported from components/user-settings/LessonPrice.vue"}, "UserSettingsPerLessonPrice": {"description": "Auto imported from components/user-settings/PerLessonPrice.vue"}, "UserSettingsPricingTableInfo": {"description": "Auto imported from components/user-settings/PricingTableInfo.vue"}, "UserSettingsQualificationSuccessDialog": {"description": "Auto imported from components/user-settings/QualificationSuccessDialog.vue"}, "UserSettingsReceiptInfo": {"description": "Auto imported from components/user-settings/ReceiptInfo.vue"}, "UserSettingsSpecialityDialog": {"description": "Auto imported from components/user-settings/SpecialityDialog.vue"}, "UserSettingsSummaryInfo": {"description": "Auto imported from components/user-settings/SummaryInfo.vue"}, "UserSettingsTeachingPreferencesInfo": {"description": "Auto imported from components/user-settings/TeachingPreferencesInfo.vue"}, "UserSettingsTeachingQualificationsInfo": {"description": "Auto imported from components/user-settings/TeachingQualificationsInfo.vue"}, "UserSettingAutocomplete": {"description": "Auto imported from components/user-settings/UserSettingAutocomplete.vue"}, "UserSettingSelect": {"description": "Auto imported from components/user-settings/UserSettingSelect.vue"}, "UserSettingTemplate": {"description": "Auto imported from components/user-settings/UserSettingTemplate.vue"}, "BusinessPageIconsDotsIcon": {"description": "Auto imported from components/business-page/icons/DotsIcon.vue"}, "ClassroomVideoTokbox": {"description": "Auto imported from components/classroom/video/Tokbox.vue"}, "ClassroomVideoTwilio": {"description": "Auto imported from components/classroom/video/Twilio.vue"}, "ClassroomVideoActions": {"description": "Auto imported from components/classroom/video/VideoActions.vue"}, "ClassroomVideoWhereby": {"description": "Auto imported from components/classroom/video/Whereby.vue"}, "ClassroomVueDraggableResizable": {"description": "Auto imported from components/classroom/vue-draggable-resizable/VueDraggableResizable.vue"}}