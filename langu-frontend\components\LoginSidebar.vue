<template>
  <v-navigation-drawer
    v-model="drawer"
    class="login-sidebar"
    hide-overlay
    right
    fixed
    temporary
    width="420"
  >
    <div class="login-sidebar-close" @click="drawer = false">
      <svg width="34" height="34" viewBox="0 0 34 34">
        <use
          :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close-big`"
        ></use>
      </svg>
    </div>
    <template v-if="!isShowForgotPassword">
      <div class="login-sidebar-title mb-2 mb-md-3">
        {{ $t('new_student') }}
      </div>
      <div class="login-sidebar-button mb-2 mb-sm-3">
        <GoogleSignInButton
          path="/user/login/google"
          :button-text="$t('sign_up_with_google')"
        />
      </div>
      <div class="login-sidebar-button mb-3 mb-sm-6">
        <v-btn
          :to="localePath({ path: '/user/register' })"
          large
          width="100%"
          color="orange"
          outlined
          class="btn-icon email"
        >
          <EmailIcon />
          {{ $t('sign_up_with_email') }}
        </v-btn>
      </div>
      <div class="login-sidebar-title">
        {{ $t('returning_student') }}
      </div>
      <div class="form-message">
        <template v-if="loginError">
          <div class="form-message-wrap form-message-wrap--error">
            <div class="form-message-icon">
              <svg width="12" height="12" viewBox="0 0 12 12">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#attention`"
                ></use>
              </svg>
            </div>
            {{ $t(loginTextError) }}
          </div>
        </template>
      </div>
      <form @submit.prevent="login">
        <div class="login-sidebar-input">
          <text-input
            v-model="email"
            height="44"
            :placeholder="$t('email_address')"
            name="login_form[_username]"
            autocomplete="email"
            @input="email = $event"
          ></text-input>
        </div>
        <div class="login-sidebar-input">
          <text-input
            v-model="password"
            height="44"
            type="password"
            :placeholder="$t('password')"
            name="login_form[_password]"
            autocomplete="current-password"
          >
            <template #append>
              <div style="margin-top: 3px">
                <v-img
                  :src="require('~/assets/images/lock-icon.svg')"
                  width="14"
                  height="21"
                ></v-img>
              </div>
            </template>
          </text-input>
        </div>
        <div class="login-sidebar-forgot">
          <div
            class="login-sidebar-forgot-text"
            @click="isShowForgotPassword = true"
          >
            {{ $t('forgot_password') }}
          </div>
          <div class="login-sidebar-forgot-button fz-18">
            <v-btn large width="154" color="orange" type="submit">
              {{ $t('log_in') }}
            </v-btn>
          </div>
        </div>
      </form>
      <div class="login-sidebar-divider">
        <span>
          {{ $t('or') }}
        </span>
      </div>
      <div class="login-sidebar-button-google mb-3 mb-sm-5 mb-md-8 fz-18">
        <GoogleSignInButton
          path="/user/login/google"
          :button-text="$t('sign_in_with_google')"
        />
      </div>
      <div class="login-sidebar-title mb-2 mb-md-3">
        {{ $t('want_to_teach_for_langu') }}
      </div>
      <div class="login-sidebar-link">
        <a href="https://heylangu-teachers.com" target="_blank">
          {{ $t('apply_now') }}
        </a>
      </div>
    </template>
    <template v-else>
      <div class="forgot">
        <div class="forgot-back" @click="isShowForgotPassword = false">
          <div class="forgot-back-icon">
            <svg width="24" height="24" viewBox="0 0 24 24">
              <use
                :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#chevron-left`"
              ></use>
            </svg>
          </div>
          {{ $t('back') }}
        </div>
        <div class="login-sidebar-title mb-2">
          {{ $t(isPasswordLinkExpired ? 'link_expired' : 'forgot_password') }}
        </div>
        <div class="forgot-text">
          {{
            $t(
              'please_enter_your_email_address_we_will_send_you_link_to_reset_your_password'
            )
          }}
        </div>
        <div class="form-message">
          <template v-if="forgotEmailStatus && forgotEmailStatus !== 200">
            <div class="form-message-wrap form-message-wrap--error">
              <div class="form-message-icon">
                <svg width="12" height="12" viewBox="0 0 12 12">
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#attention`"
                  ></use>
                </svg>
              </div>
              {{ $t('invalid_email') }}
            </div>
          </template>
        </div>
        <v-form class="d-block" @submit.prevent="forgotPassword">
          <text-input
            v-model="email"
            :placeholder="$t('email_address')"
            name="email"
            hide-details
            required
            autocomplete="email"
          ></text-input>
          <div class="mt-3">
            <v-btn type="submit" large color="orange">
              {{ $t('send') }}
            </v-btn>
          </div>
        </v-form>

        <template v-if="forgotEmailStatus && forgotEmailStatus === 200">
          <div class="forgot-success">
            {{ $t('email_sent') }}
          </div>
        </template>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script>
import GoogleSignInButton from '@/components/GoogleSignInButton.vue'
import { saveNavigationState } from '@/helpers/navigationState'
import { setTidioProperties, setVisitorData } from '~/plugins/tidio-chat'
import TextInput from '~/components/form/TextInput'
import EmailIcon from '~/components/images/EmailIcon'
// Import the navigation state helper

export default {
  name: 'LoginSidebar',
  components: { TextInput, EmailIcon, GoogleSignInButton },
  data() {
    return {
      email: '',
      password: '',
      loginTextError: '',
      forgotEmailStatus: null,
      isShowForgotPassword: false,
    }
  },
  computed: {
    drawer: {
      get() {
        return this.$store.state.isShownLoginSidebar
      },
      set(value) {
        this.$store.commit('SET_IS_LOGIN_SIDEBAR', value)

        // When opening the login sidebar, save the current navigation state
        if (value && process.client) {
          saveNavigationState()
        }
      },
    },
    user() {
      return this.$store.state.user.item
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    loginError() {
      return !!this.loginTextError.length
    },
    isPasswordLinkExpired() {
      return this.$store.state.isPasswordLinkExpired
    },
    signUpGoogleURL() {
      const utmData = this.$store.state.utmTags
      let url = '/user/login/google'

      if (utmData) {
        url += `?${Object.keys(utmData)
          .map((key) => [key, utmData[key]])
          .map((a) => [a.join('=')])
          .join('&')}`
      }

      return url
    },
  },
  watch: {
    email() {
      this.resetErrors()
    },
    password() {
      this.resetErrors()
    },
    drawer(newValue, oldValue) {
      if (!newValue) {
        this.$store.commit('SET_IS_PASSWORD_LINK_EXPIRED', false)
      }
    },
    isPasswordLinkExpired(newValue, oldValue) {
      if (newValue) {
        this.isShowForgotPassword = true
      }
    },
    isShowForgotPassword(newValue, oldValue) {
      if (!newValue) {
        this.$store.commit('SET_IS_PASSWORD_LINK_EXPIRED', false)
      }
    },
  },
  beforeDestroy() {
    this.resetErrors()

    this.email = ''
    this.password = ''
  },
  methods: {
    login() {
      this.resetErrors()

      const formData = new FormData()
      const routeRegex = this.$route.matched[0]?.regex
      // const utmData = this.$store.state.utmTags

      formData.append('login', this.email)
      formData.append('password', this.password)

      // if (utmData) {
      //   Object.keys(utmData).forEach((key) => {
      //     formData.append(key, utmData[key])
      //   })
      // }

      this.$store
        .dispatch('auth/login', formData)
        .then(() => {
          // this.$store.commit('SET_UTM_TAGS', null)
          // window.localStorage.removeItem('utm')

          this.$store.dispatch('user/getUserStatus').then(() => {
            const tidioData = this.$store.state.user.tidioData

            this.$socket.connect()
            setVisitorData({
              distinct_id: tidioData.userId,
              email: tidioData.email,
              name: tidioData.fullName,
            })
            setTidioProperties(tidioData, this.$dayjs)

            if (routeRegex.test('/') || routeRegex.test('/user/register')) {
              if (this.isTeacher) {
                this.$router.push({ path: '/user/lessons' })

                return
              }

              if (this.isStudent) {
                this.$router.push({
                  path: this.user?.upcomingLessons
                    ? '/user/lessons'
                    : '/teacher-listing',
                })

                return
              }
            }

            if (routeRegex.test('/teacher-listing/:page')) {
              // Only redirect students back to teacher listing, teachers should go to lessons
              if (this.isStudent) {
                this.$router.push({ path: '/teacher-listing' })
              } else if (this.isTeacher) {
                this.$router.push({ path: '/user/lessons' })
              }
            }
          })
        })
        .catch((e) => {
          this.loginTextError =
            e.response?.status === 406
              ? 'please_use_google_to_log_into_your_langu_account'
              : 'please_check_your_email_address_and_password_and_try_again'

          // console.log(JSON.parse(e.response.data))
        })
    },
    forgotPassword() {
      this.resetErrors()

      const formData = new FormData()

      formData.append('email', this.email)

      this.$store
        .dispatch('auth/forgotPassword', formData)
        .then((res) => {
          this.forgotEmailStatus = res
        })
        .catch((e) => {
          this.forgotEmailStatus = e.message
        })
    },
    resetErrors() {
      this.loginTextError = ''
      this.forgotEmailStatus = null
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.login-sidebar {
  padding: 83px 35px 15px;

  @media only screen and (max-width: $xxs-and-down) {
    width: 100% !important;
    padding: 70px 0 10px;
  }

  .v-navigation-drawer__content {
    padding: 5px 15px;
    margin-top: 50px !important;
  }

  .fz-18 {
    .v-btn {
      font-size: 18px !important;
    }
  }

  &-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 34px;
    height: 34px;
    color: var(--v-orange-base);
    cursor: pointer;
  }

  &-title {
    font-size: 24px;
    line-height: 1.33;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      font-size: 22px;
    }

    @media only screen and (max-width: $xsm-and-down) {
      font-size: 20px;
    }
  }

  &-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-text {
      margin-right: 5px;
      font-size: 16px;
      line-height: 1.2;
      transition: color 0.2s;
      cursor: pointer;

      &:hover {
        color: var(--v-orange-base);
      }
    }

    &-button {
      .v-btn {
        min-width: 154px !important;
      }
    }
  }

  &-divider {
    position: relative;
    margin: 24px 0;
    text-align: center;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      margin: 20px 0;
    }

    @media only screen and (max-width: $xsm-and-down) {
      margin: 18px 0;
    }

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: #4b4b4b;
      transform: translateY(-50%);
      opacity: 0.8;
    }

    span {
      position: relative;
      display: inline-block;
      padding: 0 20px;
      background-color: #fff;
    }
  }

  &-link {
    a {
      position: relative;
      display: inline-block;
      padding-right: 28px;
      font-weight: 600;
      color: var(--v-orange-base) !important;
      text-decoration: none;

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 16px;
      }

      &::before {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background-image: url('~assets/images/chevron-o.svg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
      }
    }
  }

  .btn-icon {
    &.v-btn--active {
      &:not(:hover)::before {
        opacity: 0 !important;
      }

      &:hover::before {
        opacity: 1 !important;
      }
    }

    .v-btn__content {
      &::before {
        content: '';
      }
    }

    svg {
      position: absolute;
      width: 32px;
      height: 32px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px #fff inset;
    transition: background-color 5000s ease-in-out 0s;
  }

  .forgot {
    &-back {
      display: flex;
      align-items: center;
      margin: 0 0 44px -15px;
      font-size: 20px;
      font-weight: 900;
      color: var(--v-orange-base);
      letter-spacing: 0.1px;
      line-height: 0.8;
      cursor: pointer;

      @media only screen and (max-width: $xxs-and-down) {
        margin: 0 0 35px -5px;
      }

      &-icon {
        margin-right: 12px;
      }
    }

    &-text {
      font-size: 16px;
      color: rgba(45, 45, 45, 0.7);
      line-height: 1.5;
    }

    &-success {
      margin-top: 12px;
      font-size: 16px;
      color: var(--v-success-base);
    }
  }
}
</style>
