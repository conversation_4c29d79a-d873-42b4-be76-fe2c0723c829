<template>
  <div :class="['popup-load-files elevation-3', role]" :style="{ zIndex }">
    <div
      ref="loadFilesHeader"
      class="popup-load-files-header"
      :class="{ inactive: tickedFiles.length > 0 }"
    >
      <div class="popup-load-files-title">
        {{ $t('library') }}
      </div>
      <div class="popup-load-files-header-buttons">
        <div class="popup-load-files-search-wrap">
          <input
            v-model="queryStr"
            class="popup-load-files-search popup-load-files-input"
            placeholder="Search"
          />
          <button id="library-add-search" class="popup-load-files-search-icon">
            <span class="d-block library-add-search-img"></span>
          </button>
        </div>
        <div class="popup-load-files-select-wrap">
          <div
            class="popup-load-files-input popup-load-files-select cursor-pointer"
            @click="toggleSortOptionsList"
          >
            {{ $t('sort_by') }}
          </div>
          <div
            :class="{ active: sortListDisplay }"
            class="popup-load-files-select-options"
          >
            <div
              v-for="(option, idx) in sortOptions"
              :key="idx"
              class="cursor-pointer"
              :class="{
                'popup-load-files-select-option':
                  requestBody.sort_type === option.value,
              }"
              @click="changeSortType(option)"
            >
              {{ option.label }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      ref="headerSelectedFiles"
      class="popup-load-files-header-selected-files"
      :class="{ active: tickedFiles.length > 0 }"
    >
      <button
        id="add-to-classroom"
        class="popup-load-files-input cursor-pointer"
        @click="addToClassroom"
      >
        {{ $t('add_to_classroom') }}
      </button>
      <div class="popup-load-files-buttons-wrap">
        <button
          id="popup-load-files-download"
          class="popup-load-files-input cursor-pointer"
          @click="downloadFiles"
        >
          {{ $t('download') }}
        </button>
        <button
          class="popup-load-files-input cursor-pointer"
          @click="deleteFiles"
        >
          {{ $t('delete') }}
        </button>
        <v-btn
          class="popup-load-files-header-cross cursor-pointer"
          icon
          color="white"
          @click="uncheckFiles"
        >
          <svg width="28" height="28" viewBox="0 0 21 20">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close`"
            ></use>
          </svg>
        </v-btn>
      </div>
    </div>

    <div
      class="popup-load-files-wrap"
      @dragover.stop.prevent="isDragging = true"
    >
      <div class="popup-load-files-body">
        <div
          v-show="isDragging"
          class="popup-load-files-drop-wrap active"
          @dragleave.stop.prevent="isDragging = false"
          @drop.stop.prevent="handleFileDrop"
        >
          <div class="drop-area--wrapper">
            <img
              :src="require('~/assets/images/classroom/dropfiles.svg')"
              class="drop-area--wrapper__dropbox-img"
              alt=""
            />
          </div>
        </div>

        <div>
          <div id="popup-load-files-list" class="popup-load-files-list">
            <template v-if="uploadingFiles">
              <div
                v-for="(loadingFile, idx) in uploadingFiles"
                :key="idx"
                class="popup-load-files-item popup-load-files-item--loading"
              >
                <div class="popup-load-files-item-helper">
                  <div class="popup-load-files-item-img">
                    <v-progress-circular
                      :rotate="360"
                      :size="75"
                      :width="8"
                      :indeterminate="loadingFile.uploadPercentage === 0"
                      :value="loadingFile.uploadPercentage"
                      color="success"
                    >
                      <template v-if="loadingFile.uploadPercentage > 0">
                        {{ loadingFile.uploadPercentage }}%
                      </template>
                    </v-progress-circular>
                    <div
                      v-show="loadingFile.uploadPercentage > 0"
                      class="popup-load-files-item-cancel cursor-pointer"
                      @click="cancelUpload(loadingFile)"
                    >
                      <div class="popup-load-files-tick-icon">
                        <svg width="15" height="15" viewBox="0 0 21 20">
                          <use
                            :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close`"
                          ></use>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="popup-load-files-item-name">
                  <p>{{ loadingFile.name }}</p>
                </div>
              </div>
            </template>
            <div
              v-for="file in files"
              :key="file.id"
              class="popup-load-files-item"
            >
              <div class="popup-load-files-item-helper">
                <div
                  class="popup-load-files-item-img cursor-pointer"
                  @click="toggleFileMark(file)"
                >
                  <template v-if="isPdf(file)">
                    <canvas
                      :id="`pdf-thumb--${file.id}`"
                      :title="file.displayName"
                    ></canvas>
                  </template>
                  <template v-else-if="isAudio(file)">
                    <v-img
                      :src="
                        require('~/assets/images/classroom/volume-high.svg')
                      "
                      max-width="60"
                      :title="file.displayName"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="`${rootUrl}${file.path}`"
                      max-width="75%"
                      max-height="75%"
                      :title="file.displayName"
                    ></v-img>
                  </template>

                  <div
                    class="popup-load-files-item-tick"
                    :class="{
                      active: tickedFiles.find((item) => item.id === file.id),
                    }"
                  >
                    <div class="popup-load-files-tick-icon">
                      <img
                        :src="require('~/assets/images/classroom/tick2.svg')"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="popup-load-files-item-name">
                <p :title="file.displayName">{{ file.displayName }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="popup-load-files-footer">
        <div
          v-if="arrayPages.length > 1"
          class="popup-load-files-footer-pagination"
        >
          <button
            class="popup-load-files-btn-nav popup-load-files-btn-nav-prev cursor-pointer"
            :disabled="requestBody.page === 1"
            @click="prevPage"
          >
            <span
              class="d-block popup-load-files-nav-icon popup-load-files-nav-icon-prev"
            ></span>
            <span>{{ $t('previous') }}</span>
          </button>
          <div
            id="popup-load-files-navigation"
            class="popup-load-files-nav-wrap"
          >
            <span
              v-for="(page, idx) in arrayPages"
              :key="idx"
              class="popup-load-files-nav-number"
              :class="{ active: requestBody.page === page }"
              @click="goToPage(page)"
            >
              {{ page }}
            </span>
          </div>
          <button
            class="popup-load-files-btn-nav popup-load-files-btn-nav-next cursor-pointer"
            :disabled="requestBody.page === arrayPages.length"
            @click="nextPage"
          >
            <span>{{ $t('next') }}</span>
            <span
              class="d-block popup-load-files-nav-icon popup-load-files-nav-icon-next"
            ></span>
          </button>
        </div>
        <div class="popup-load-files-footer-buttons">
          <label
            class="popup-load-files-label-upload cursor-pointer font-weight-medium"
          >
            {{ $t('upload_new_file') }}
            <input
              id="upload-library-files"
              ref="file"
              type="file"
              :accept="acceptedFilesStr"
              multiple
              class="popup-load-files-btn-upload"
              @change="addFiles"
            />
          </label>
          <v-btn
            color="primary"
            small
            class="font-weight-medium popup-load-files-close cursor-pointer"
            @click="closeLibrary"
          >
            {{ $t('cancel') }}
          </v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getFileExtension, debounce } from '~/helpers'
import { MAX_FILE_SIZE } from '~/helpers/constants'

export default {
  name: 'Library',
  props: {
    viewportWidth: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      pdfjsLib: {},
      rootUrl: process.env.NUXT_ENV_URL,
      files: [],
      totalPages: 1,
      sortListDisplay: false,
      queryStr: '',
      requestBody: {
        query: '',
        sort_direction: 'DESC',
        page: 1,
        sort_type: 3,
      },
      sortOptions: [
        {
          label: this.$t('last_added'),
          value: 3,
        },
        {
          label: this.$t('last_opened'),
          value: 'last_opened',
        },
        {
          label: this.$t('file_name_a_z'),
          value: 2,
        },
        {
          label: this.$t('file_name_z_a'),
          value: 1,
        },
      ],
      tickedFiles: [],
      arrayPages: [],
      uploadPercentage: 0,
      isDragging: false,
      uploadingFiles: [],
    }
  },
  computed: {
    zIndex() {
      return this.$store.state.classroom.maxIndex + 1
    },
    getZoom() {
      return this.$store.getters['classroom/zoomAsset'].asset
    },
    lessonId() {
      return this.$store.state.classroom.lessonId
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    acceptedFiles() {
      return this.$store.state.classroom.acceptedFiles
    },
    acceptedFilesStr() {
      return this.$store.getters['classroom/acceptedFilesStr']
    },
  },
  watch: {
    queryStr: debounce(function (value) {
      this.requestBody.query = value

      this.getListOfFiles()
    }, 1000),
  },
  async beforeMount() {
    this.pdfjsLib = await require('pdfjs-dist')
    this.pdfjsLib.GlobalWorkerOptions.workerSrc = await require('pdfjs-dist/build/pdf.worker.entry')

    this.getListOfFiles()
  },
  methods: {
    getPager(currentPage = 1, pageSize = 10, maxPages = 10) {
      // calculate total pages
      const totalPages = this.totalPages

      // ensure current page isn't out of range
      if (currentPage < 1) {
        currentPage = 1
      } else if (currentPage > totalPages) {
        currentPage = totalPages
      }

      let startPage, endPage
      if (totalPages <= maxPages) {
        // total pages less than max so show all pages
        startPage = 1
        endPage = totalPages
      } else {
        // total pages more than max so calculate start and end pages
        const maxPagesBeforeCurrentPage = Math.floor(maxPages / 2)
        const maxPagesAfterCurrentPage = Math.ceil(maxPages / 2) - 1

        if (currentPage <= maxPagesBeforeCurrentPage) {
          // current page near the start
          startPage = 1
          endPage = maxPages
        } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
          // current page near the end
          startPage = totalPages - maxPages + 1
          endPage = totalPages
        } else {
          // current page somewhere in the middle
          startPage = currentPage - maxPagesBeforeCurrentPage
          endPage = currentPage + maxPagesAfterCurrentPage
        }
      }

      // create an array of pages to ng-repeat in the pager control
      // return array of pages required by the view
      return Array.from(Array(endPage + 1 - startPage).keys()).map(
        (i) => startPage + i
      )
    },
    uncheckFiles() {
      this.tickedFiles = []
    },
    toggleFileMark(file) {
      if (this.tickedFiles.find((item) => item.id === file.id)) {
        this.tickedFiles = this.tickedFiles.filter(
          (item) => item.id !== file.id
        )
      } else {
        this.tickedFiles.push(file)
      }
    },
    downloadFiles() {
      this.tickedFiles.forEach((file) => {
        const link = document.createElement('a')

        link.href = file.path
        link.download = file.displayName

        link.click()
      })
    },
    deleteFiles() {
      const data = this.tickedFiles.map((file) => file.id)

      this.$store.dispatch('classroom/deleteFiles', data).then((response) => {
        this.getListOfFiles()

        this.tickedFiles = []
      })
    },
    toggleSortOptionsList() {
      return (this.sortListDisplay = !this.sortListDisplay)
    },
    changeSortType(option) {
      this.requestBody.sort_type = option.value

      return this.getListOfFiles()
    },
    nextPage() {
      if (this.requestBody.page === this.totalPages) return false

      this.requestBody.page++

      return this.getListOfFiles()
    },
    prevPage() {
      if (this.requestBody.page === 1) return false

      this.requestBody.page--

      return this.getListOfFiles()
    },
    goToPage(page) {
      this.requestBody.page = page

      return this.getListOfFiles()
    },
    isPdf(file) {
      return getFileExtension(file.path) === 'pdf'
    },
    isAudio(file) {
      const ext = getFileExtension(file.path)

      return ext === 'mp3' || ext === 'wav'
    },
    getListOfFiles() {
      const formData = new FormData()

      for (const key in this.requestBody) {
        formData.append(key, this.requestBody[key])
      }

      this.$store
        .dispatch('classroom/getListOfFiles', {
          formData,
          page: this.requestBody.page,
        })
        .then((files) => {
          this.totalPages = files.length ? files[0].pages : 1
          this.files = files
          this.arrayPages = this.getPager(this.requestBody.page, 18, 5)

          this.files.forEach((file) => {
            if (this.isPdf(file)) {
              this.makePdfThumb(file)
            }
          })
        })
    },
    makePdfThumb(file) {
      this.pdfjsLib
        .getDocument(this.rootUrl + file.path)
        .promise.then((pdfDoc) => {
          pdfDoc.getPage(1).then((page) => {
            if (page) {
              const vp = page.getViewport({ scale: 1 })
              const canvas = document.querySelector(`#pdf-thumb--${file.id}`)

              if (canvas && vp.width > 0 && vp.height > 0) {
                const pageScale = vp.width / vp.height

                canvas.height = canvas.width = 78

                if (vp.width < vp.height) {
                  canvas.width = 78 * pageScale
                } else {
                  canvas.height = 78 / pageScale
                }

                const scale = Math.min(
                  canvas.width / vp.width,
                  canvas.height / vp.height
                )

                page.render({
                  canvasContext: canvas.getContext('2d'),
                  viewport: page.getViewport({ scale }),
                })
              }
            }
          })
        })
        .catch((err) => {
          console.log(err.message)
        })
    },
    handleFileDrop(e) {
      const droppedFiles = e.dataTransfer.files

      if (droppedFiles?.length > 0) {
        this.uploadFiles(droppedFiles)
      }
    },
    addFiles() {
      const files = this.$refs.file?.files

      if (files?.length > 0) {
        this.uploadFiles(files)
      }
    },
    cancelUpload(fileData) {
      fileData.source.cancel('Operation canceled by user.')

      this.uploadingFiles = this.uploadingFiles.filter(
        (file) => file.id !== fileData.id
      )
    },
    async uploadFiles(files) {
      await this.$store.dispatch('loadingAllow', false)

      files = [...files]
      this.isDragging = false

      for (let i = 0; i <= files.length - 1; i++) {
        const id = Math.floor(Math.random() * 999999)
        const file = files[i]
        const fileExtension = getFileExtension(file.name)
        const cancelToken = this.$axios.CancelToken
        const source = cancelToken.source()
        const uploadingFileData = {
          id,
          source,
          name: file.name,
          uploadPercentage: 0,
        }
        const formData = new FormData()

        if (file.size > MAX_FILE_SIZE) {
          await this.$store.dispatch('snackbar/error', {
            errorMessage: this.$t('file_exceeds_max_size_10mb'),
            timeout: 5000,
          })

          continue
        }

        this.uploadingFiles.push(uploadingFileData)

        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {
          const { data, fileName } = await this.$store.dispatch(
            'classroom/convertOfficeToPdf',
            file
          )

          formData.append('file', new Blob([data]), fileName)
        } else {
          formData.append('file', file)
        }

        await this.$axios
          .post(
            `${process.env.NUXT_ENV_API_URL}/lesson/classroom/upload/library/${this.lessonId}`,
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              cancelToken: source.token,
              onUploadProgress: (progressEvent) => {
                uploadingFileData.uploadPercentage = parseInt(
                  Math.round((progressEvent.loaded / progressEvent.total) * 100)
                )
              },
              progress: false,
            }
          )
          .then((response) => {
            this.uploadingFiles = this.uploadingFiles.filter(
              (file) => file.id !== id
            )
            this.files = [...response.data, ...this.files]

            this.tickedFiles.push(...response.data)
            response.data.forEach((file) => {
              if (this.isPdf(file)) {
                this.makePdfThumb(file)
              }
            })
          })
          .catch((e) => console.log(e))

        await this.$store.dispatch('loadingAllow', true)
      }
    },
    closeLibrary() {
      this.$store.commit('classroom/toggleLibrary')
    },
    async addToClassroom() {
      let offsetX = 0
      let offsetY = 0

      for (const file of this.tickedFiles) {
        const index = this.$store.state.classroom.maxIndex + 1
        const ext = getFileExtension(file.path)

        let type

        if (this.acceptedFiles.pdfTypes.includes(ext)) {
          type = 'pdf'
        } else if (this.acceptedFiles.imageTypes.includes(ext)) {
          type = 'image'
        } else if (this.acceptedFiles.audioTypes.includes(ext)) {
          type = 'audio'
        } else {
          return
        }

        await this.$store.dispatch('classroom/createAsset', {
          type,
          index,
          path: file.path,
          displayName: file.displayName,
          owner: this.role,
          top:
            this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100,
          left: this.viewportWidth / 2 + this.getZoom.x + offsetX - 250,
        })
        this.$store.commit('classroom/setMaxIndex', index)

        offsetX += 50
        offsetY += 50
      }

      this.uncheckFiles()
      this.closeLibrary()
    },
  },
}
</script>

<style lang="scss" scoped>
.popup-load-files-item-img .preview-fluid {
  max-width: 75%;
  max-height: 75%;
}

.popup-load-files-header.inactive {
  display: none;
}
</style>
