<template>
  <lesson-price
    :value="value"
    :rules="rules"
    :length="length"
    @input="updateValue"
  ></lesson-price>
</template>

<script>
import LessonPrice from '@/components/user-settings/LessonPrice'

export default {
  name: 'PerLessonPrice',
  components: { LessonPrice },
  props: {
    items: {
      type: Array,
      required: true,
    },
    length: {
      type: Number,
      required: true,
    },
    lessons: {
      type: Number,
      required: true,
    },
    rules: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      key: this.length + this.lessons,
      keyCode: null,
    }
  },
  computed: {
    value() {
      return this.items.find(
        (item) => item.length === this.length && item.lessons === this.lessons
      )?.price
    },
  },
  methods: {
    updateValue(value) {
      this.$store.commit('settings/UPDATE_LESSON_PRICE', {
        value,
        length: this.length,
        lessons: this.lessons,
      })
    },
  },
}
</script>
