/**
 * Language translation mappings for converting language names to i18n translation keys
 * Used across components for consistent language name translations
 */

export const LANGUAGE_TRANSLATION_MAP = {
  // Main language keys (from the root level of translation files)
  English: 'english',
  Spanish: 'spanish',
  French: 'french',
  German: 'germany', // Note: the key is 'germany' not 'german' in translation files
  Italian: 'italian',
  Portuguese: 'portuguese',
  Russian: 'russian',
  Chinese: 'chinese',
  Japanese: 'japanese',
  Korean: 'korean',
  Arabic: 'arabic',
  Dutch: 'dutch',
  Polish: 'polish',
  Swedish: 'swedish',
  Norwegian: 'norwegian',
  Danish: 'danish',
  Greek: 'greek',
  Turkish: 'turkish',
  Thai: 'thai',
  Ukrainian: 'ukrainian',
  Belarusian: 'belarusian',
  Czech: 'czech',
  Hungarian: 'hungarian',
  Bulgarian: 'bulgarian',
  Croatian: 'croatian',
  Serbian: 'serbian',
  Slovak: 'slovak',
  Welsh: 'welsh',
  Catalan: 'catalan',
  Bosnian: 'bosnian',
  Afrikaans: 'afrikaans',
  // Language names from the language_names section
  'Portuguese (Portugal)': 'portuguese-portugal',
  'Portuguese (Brazil)': 'portuguese-brazil',
  'Persian (Farsi)': 'persian-farsi',
  'Chinese (Mandarin)': 'chinese-mandarin',
  'Chinese (Cantonese)': 'chinese-cantonese',
  'Ancient Greek': 'ancient-greek',
}

/**
 * Polish language name translations
 * Maps English language names to their Polish equivalents
 */
export const POLISH_LANGUAGE_NAMES = {
  English: 'angielskiego',
  Ukrainian: 'ukraińskiego',
  Swedish: 'szwedzkiego',
  Spanish: 'hiszpańskiego',
  Russian: 'rosyjskiego',
  'Portuguese (Portugal)': 'portugalskiego (Portugalia)',
  'Portuguese (Brazil)': 'portugalskiego (Brazylia)',
  Portuguese: 'portugalskiego',
  Polish: 'polskiego',
  'Persian (Farsi)': 'perskiego (farsi)',
  Norwegian: 'norweskiego',
  Japanese: 'japońskiego',
  Korean: 'koreańskiego',
  Italian: 'włoskiego',
  Hungarian: 'węgierskiego',
  Greek: 'greckiego',
  German: 'niemieckiego',
  French: 'francuskiego',
  Dutch: 'niderlandzkiego',
  Danish: 'duńskiego',
  Czech: 'czeskiego',
  Croatian: 'chorwackiego',
  'Chinese (Mandarin)': 'chińskiego (mandaryńskiego)',
  'Chinese (Cantonese)': 'chińskiego (kantońskiego)',
  Chinese: 'chińskiego',
  Catalan: 'katalońskiego',
  Bulgarian: 'bułgarskiego',
  Bosnian: 'bośniackiego',
  Arabic: 'arabskiego',
  'Ancient Greek': 'starogreckiego',
  Mathematics: 'matematyki', // Special case for math tutoring
  Belarusian: 'białoruskiego',
  Turkish: 'tureckiego',
  Thai: 'tajskiego',
  Slovak: 'słowackiego',
  Welsh: 'walijskiego',
  Afrikaans: 'afrykanerskiego (Afrikaans)',
  Serbian: 'serbskiego',
}

/**
 * Translates a language name to the current locale using Vue i18n
 * @param {string} languageName - The language name to translate
 * @param {object} i18n - Vue i18n instance (this.$t)
 * @param {string} locale - Current locale (optional, will be detected from i18n if not provided)
 * @returns {string} - Translated language name or original name if no translation found
 */
export function translateLanguageName(languageName, i18n, locale = null) {
  // Safety check for i18n function
  if (!i18n || typeof i18n !== 'function') {
    return languageName
  }

  // Detect current locale if not provided
  let currentLocale = locale
  if (!currentLocale) {
    try {
      // Try to get locale from i18n context
      currentLocale = i18n.locale || 'en'
    } catch (error) {
      currentLocale = 'en'
    }
  }

  // For Polish locale, use the direct Polish translations first
  if (currentLocale === 'pl' && POLISH_LANGUAGE_NAMES[languageName]) {
    return POLISH_LANGUAGE_NAMES[languageName]
  }

  // Get the translation key for the language name
  const translationKey = LANGUAGE_TRANSLATION_MAP[languageName]

  // If we have a translation key, try to translate it
  if (translationKey) {
    try {
      const translated = i18n(translationKey)
      // If translation exists and is different from the key, use it
      if (translated && translated !== translationKey) {
        return translated
      }
    } catch (error) {}
  }

  const languageNamesKey = `language_names.${
    translationKey || languageName.toLowerCase()
  }`
  try {
    const languageNamesTranslated = i18n(languageNamesKey)
    if (
      languageNamesTranslated &&
      languageNamesTranslated !== languageNamesKey
    ) {
      return languageNamesTranslated
    }
  } catch (error) {}

  // Fallback to original language name if no translation found
  return languageName
}
