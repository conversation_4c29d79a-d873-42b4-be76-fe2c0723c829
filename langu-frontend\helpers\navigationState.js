/**
 * Navigation state management for registration flow
 * Stores and retrieves the user's navigation state to return them to the same page after registration
 */

// Save the current navigation state before registration
export const saveNavigationState = () => {
  if (!process.client) return

  const currentPath = window.location.pathname
  const currentQuery = window.location.search
  const currentHash = window.location.hash
  const currentState = {
    path: currentPath,
    query: currentQuery,
    hash: currentHash,
    timestamp: Date.now(),
    // Store additional state information for specific pages
    modalState: null,
  }

  // For teacher profile pages, store additional state information
  if (currentPath.includes('/teacher/')) {
    // Check if time picker or summary dialog is open
    const step = new URLSearchParams(currentQuery).get('step')
    if (step) {
      currentState.modalState = step
    }

    // Store selected slots if available
    const selectedSlots = localStorage.getItem('selected-slots')
    if (selectedSlots) {
      currentState.selectedSlots = selectedSlots
    }

    // Store current course if available
    const currentCourse = localStorage.getItem('current-course')
    if (currentCourse) {
      currentState.currentCourse = currentCourse
    }
  }

  localStorage.setItem('navigation_state', JSON.stringify(currentState))
}

// Retrieve and clear the saved navigation state
export const getNavigationState = () => {
  if (!process.client) return null

  const stateJson = localStorage.getItem('navigation_state')
  if (!stateJson) return null

  try {
    const state = JSON.parse(stateJson)

    // Check if state is too old (more than 30 minutes)
    const now = Date.now()
    if (now - state.timestamp > 30 * 60 * 1000) {
      localStorage.removeItem('navigation_state')
      return null
    }

    return state
  } catch (e) {
    localStorage.removeItem('navigation_state')
    return null
  }
}

// Clear the navigation state
export const clearNavigationState = () => {
  if (!process.client) return
  localStorage.removeItem('navigation_state')
}

// Apply the navigation state after registration
export const applyNavigationState = (router, shouldClearState = false) => {
  const state = getNavigationState()
  if (!state) return false

  const path = state.path
  const query = {}

  // Parse query parameters
  if (state.query) {
    const searchParams = new URLSearchParams(state.query)
    searchParams.forEach((value, key) => {
      query[key] = value
    })
  }

  // Add modal state if it exists
  if (state.modalState) {
    query.step = state.modalState
  }

  // Navigate to the saved path with query parameters
  router.push({ path, query })

  // Clear the navigation state

  if (shouldClearState) {
    clearNavigationState()
  }

  return true
}
