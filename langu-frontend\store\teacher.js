export const state = () => ({
  items: [],
  totalQuantity: 0,
})

export const mutations = {
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
  SET_TOTAL_QUANTITY: (state, payload) => {
    state.totalQuantity = payload
  },
}

export const getters = {
  totalPages: (state) =>
    Math.ceil(state.totalQuantity / process.env.NUXT_ENV_PER_PAGE),
}

export const actions = {
  getHomePageTeachers({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-teachers`
    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data)) {
          commit('SET_ITEMS', data)

          return data
        }

        return []
      })
      .catch((e) => console.log(e))
  },
  getTeachers({ commit, rootState }, { page, perPage, params, searchQuery }) {
    let url = `${process.env.NUXT_ENV_API_URL}/teacher-listing/${page}/${perPage}`

    if (params.length) {
      url += `/${params}`
    }

    if (searchQuery) {
      url += `;searchText,${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        let teachers = data?.teachers ?? []
        const originalTotalCount = data?.countTeachers ?? 0

        // Client-side filtering when specialities are selected
        const selectedSpecialities =
          rootState.teacher_filter.selectedSpecialities
        if (selectedSpecialities && selectedSpecialities.length > 0) {
          teachers = teachers.filter((teacher) => {
            if (teacher.specialities && Array.isArray(teacher.specialities)) {
              const hasSpeciality = selectedSpecialities.some((selectedSpec) =>
                teacher.specialities.some(
                  (teacherSpecObj) =>
                    teacherSpecObj.speciality &&
                    teacherSpecObj.speciality.id === selectedSpec.id
                )
              )
              if (hasSpeciality) {
                const reorderedSpecialties = [...teacher.specialities]
                selectedSpecialities.forEach((selectedSpec) => {
                  const selectedIndex = reorderedSpecialties.findIndex(
                    (spec) =>
                      spec.speciality && spec.speciality.id === selectedSpec.id
                  )
                  if (selectedIndex > -1) {
                    const [selected] = reorderedSpecialties.splice(
                      selectedIndex,
                      1
                    )
                    reorderedSpecialties.unshift(selected)
                  }
                })
                teacher.specialities = reorderedSpecialties
              }
              return hasSpeciality
            }
            return false
          })
          commit('SET_ITEMS', teachers)
          commit('SET_TOTAL_QUANTITY', originalTotalCount)
        } else {
          commit('SET_ITEMS', teachers)
          commit('SET_TOTAL_QUANTITY', originalTotalCount)
        }
      })
  },
}
