<?php

declare(strict_types=1);

namespace App\ApiBundle\Controller;

use App\ApiBundle\Factory\TeacherSearchDataFactory;
use App\ApiBundle\Handler\TeacherListHandler;
use App\AppBundle\Interfaces\CommandBusInterface;
use App\AppBundle\Message\Query\TeacherListQueryMessage;
use App\AppBundle\Provider\FaqTeacherListProvider;
use InvalidArgumentException;
use JMS\Serializer\Serializer;
use JMS\Serializer\SerializerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Throwable;

/**
 * <AUTHOR> <<EMAIL>>
 */
class TeacherListController extends AbstractApiController
{
    /**
     * @var TeacherListHandler
     */
    private $teacherListHandler;

    /**
     * @var TeacherSearchDataFactory
     */
    private $teacherSearchDataFactory;

    /**
     * @var FaqTeacherListProvider
     */
    private $faqTeacherListProvider;

    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    public function __construct(
        SerializerInterface $serializer,
        LoggerInterface $logger,
        TeacherListHandler $teacherListHandler,
        TeacherSearchDataFactory $teacherSearchDataFactory,
        FaqTeacherListProvider $faqTeacherListProvider,
        CommandBusInterface $commandBus
    ) {
        parent::__construct($serializer, $logger);

        $this->teacherListHandler = $teacherListHandler;
        $this->teacherSearchDataFactory = $teacherSearchDataFactory;
        $this->faqTeacherListProvider = $faqTeacherListProvider;
        $this->commandBus = $commandBus;
    }

    public function getTeacherList(int $page, int $perPage, string $searchString): JsonResponse
    {
        try {
            return $this->getSuccessResponse($this->teacherListHandler->getTeachersDataByCriteria($searchString, $page,
                $perPage));
        } catch (Throwable $exception) {
            return $this->getErrorResponse($exception);
        }
    }

    public function getTeacherListNew($page, $perPage, string $searchString): JsonResponse
    {
        try {
            $message = new TeacherListQueryMessage($searchString, (int)$page, (int)$perPage, $this->getUser());
            $this->commandBus->handle($message);

            return $this->getSuccessResponse($message->getView());
        } catch (Throwable $exception) {
            return $this->getErrorResponse($exception);
        }
    }


    public function getDataForSearchTeachers(): JsonResponse
    {
        try {
            return $this->getSuccessResponse($this->teacherSearchDataFactory->create());
        } catch (Throwable $exception) {
            return $this->getErrorResponse($exception);
        }
    }

    public function getFaqTeacherListPage(): JsonResponse
    {
        try {
            return $this->getSuccessResponse($this->faqTeacherListProvider->getPublishFaq());
        } catch (Throwable $exception) {
            return $this->getErrorResponse($exception);
        }
    }
}
