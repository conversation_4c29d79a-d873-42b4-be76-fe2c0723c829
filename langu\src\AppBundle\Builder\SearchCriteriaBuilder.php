<?php

declare(strict_types=1);

namespace App\AppBundle\Builder;

use App\IntlBundle\Model\LanguageManager;
use App\MoneyBundle\Model\CurrencyManager;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;
use App\UserBundle\Manager\ProficiencyManager;
use App\UserBundle\Model\SpecialityManager;

/**
 * <AUTHOR> <<EMAIL>>
 */
class SearchCriteriaBuilder
{
    /**
     * @var OptionsResolver
     */
    private $resolver;

    /**
     * @var LanguageManager
     */
    private $languageManager;

    /**
     * @var CurrencyManager
     */
    private $currencyManager;

    /**
     * @var SpecialityManager
     */
    private $specialityManager;

    /**
     * @var ProficiencyManager
     */
    private $proficiencyManager;

    public function __construct(
        LanguageManager $languageManager,
        CurrencyManager $currencyManager,
        SpecialityManager $specialityManager,
        ProficiencyManager $proficiencyManager
    ) {
        $this->languageManager = $languageManager;
        $this->currencyManager = $currencyManager;
        $this->specialityManager = $specialityManager;
        $this->proficiencyManager = $proficiencyManager;
    }

    public function build(array $data): array
    {
        // Map slug-based parameters to regular parameters
        $data = $this->mapSlugParameters($data);

        $resolver = $this->getOptionsResolver();

        return $resolver->resolve($data);
    }

    private function mapSlugParameters(array $data): array
    {
        // If we have specialitySlug, map it to speciality
        if (isset($data['specialitySlug'])) {
            $speciality = $this->specialityManager->getSpecialityBySlug($data['specialitySlug'][0]);
            if ($speciality) {
                $data['speciality'] = [$speciality->getId()];
            }
            unset($data['specialitySlug']);
        }

        // If we have languageSlug, map it to language
        if (isset($data['languageSlug'])) {
            $language = $this->languageManager->getLanguageBySlug($data['languageSlug'][0]);
            if ($language) {
                $data['language'] = $language->getId();
            }
            unset($data['languageSlug']);
        }



        return $data;
    }

    private function getOptionsResolver(): OptionsResolver
    {
        if (!$this->resolver) {
            $allowedCriteria = [
                'language',
                'proficiency',
                'motivation',
                'teacherPreference',
                'matchLanguages',
                'currency',
                'speciality',
                'proficiencyLevels',
                'sortOption',
                'dates',
                'time',
                'tag',
                'companyTag',
                'searchText'
            ];

            $languageManager = $this->languageManager;
            $currencyManager = $this->currencyManager;
            $specialityManager = $this->specialityManager;
            $proficiencyManager = $this->proficiencyManager;

            $languageNormalizer = static function (Options $options, $value) use ($languageManager) {
                return $languageManager->getReferenceById($value[0]);
            };

            $languagesNormalizer = static function (Options $options, $value) use ($languageManager) {
                if (is_array($value)) {
                    $references = [];
                    foreach ($value as $item) {
                        $references[] = $languageManager->getReferenceById($item);
                    }

                    return $references;
                }

                return [
                    $languageManager->getReferenceById($value)
                ];
            };

            $specialitiesNormalizer = static function (Options $options, $value) use ($specialityManager) {
                return $specialityManager->getSpecialitiesByIds($value);
            };

            $proficiencyNormalizer = static function (Options $options, $value) use ($proficiencyManager) {
                return $proficiencyManager->getReferenceById($value[0]);
            };

            $booleanNormalizer = static function (Options $options, $value) {
                return (bool)$value;
            };

            $integerNormalizer = static function (Options $options, $value) {
                return (int)$value[0];
            };

            $stringNormalizer = static function (Options $options, $value) {
                return (string)$value[0];
            };

            $arrayNormalizer = static function (Options $options, $value) {
                return $value;
            };

            $resolver = new OptionsResolver();
            $resolver->setDefined($allowedCriteria);
            $resolver->setNormalizer('language', $languageNormalizer);
            $resolver->setNormalizer('proficiencyLevels', $proficiencyNormalizer);
            $resolver->setNormalizer('motivation', $integerNormalizer);
            $resolver->setNormalizer('proficiency', $integerNormalizer);
            $resolver->setNormalizer('teacherPreference', $integerNormalizer);
            $resolver->setNormalizer('matchLanguages', $languagesNormalizer);
            $resolver->setNormalizer('speciality', $specialitiesNormalizer);
            $resolver->setNormalizer('currency', static function (Options $options, $value) use ($currencyManager) {
                return $currencyManager->getReferenceById($value[0]);
            });
            $resolver->setNormalizer('sortOption', $integerNormalizer);
            $resolver->setNormalizer('dates', $arrayNormalizer);
            $resolver->setNormalizer('time', $arrayNormalizer);
            $resolver->setNormalizer('tag', $integerNormalizer);
            $resolver->setNormalizer('companyTag', $integerNormalizer);
            $resolver->setNormalizer('searchText', $stringNormalizer);


            $this->resolver = $resolver;
        }

        return $this->resolver;
    }
}
