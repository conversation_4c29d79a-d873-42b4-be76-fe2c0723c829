<table class="table table-striped table-hover">
    <thead>
        <tr>
            <th>Date</th>
            <th>User</th>
            <th>Last message</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        {% for thread in threads %}
            {% set lastMessage = thread.messages.slice(0, 1) %}
            {% set lastMessage = lastMessage|length ? lastMessage|first : thread %}
            {% set author = app.user.isEqualTo(lastMessage.recipient) ? false : true %}
            <tr>
                <td>
                    {{ lastMessage.date|userlocalizeddate }}
                </td>
                <td>
                    {% if thread.isSystem %}
                        <span>--</span>
                    {% else %}
                        {% if author %}
                            {% if 'ROLE_TEACHER' in lastMessage.recipient.roles %}
                                <a href="{{ path('teacher.profile.view', {'username': lastMessage.recipient.username}) }}">
                                    {{ lastMessage.recipient.fullname }}
                                </a>
                            {% else %}
                                <span>
                                    {{ lastMessage.recipient.fullname }}
                                </span>
                            {% endif %}
                        {% else %}
                            {% if 'ROLE_TEACHER' in lastMessage.author.roles %}
                                <a href="{{ path('teacher.profile.view', {'username': lastMessage.author.username}) }}">
                                    {{ lastMessage.author.fullname }}
                                </a>
                            {% else %}
                                <span>
                                    {{ lastMessage.author.fullname }}
                                </span>
                            {% endif %} 
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    <a href="{{ path('user.thread', {threadId: thread.id}) }}">{{ lastMessage.message }}</a></td>
                <td>
                    {% if lastMessage.readDate is null %}
                        {% if author %}
                            <span class="label label-danger">Unread</span>
                        {% else %}
                            <span class="label label-primary">New</span>
                        {% endif %}
                    {% endif %}
                    {% if thread.isSystem %}
                        <span class="label label-default">System</span>
                    {% endif %}
                </td>
            </tr>
        {% else %}
            <tr>
                <td colspan="3">
                    <span>No messages to display</span>
                </td>
            </tr>
        {% endfor %}
    </tbody>
</table>
<div class="navigation">
    {{ paginate(threads) }}
</div>