{% extends '@User/Mails/base.html.twig' %}
{% trans_default_domain 'mail' %}
{% block title %}
    {{ 'student_book_lesson_title'|trans({}, 'mail', recipientLanguage) }}
{% endblock %}
{% block inner_content %}
    <table class="row"><tbody><tr>
        <th class="small-12 large-12 columns first last"><table><tr><th>
            <p>
                {% autoescape false %}{{ 'student_book_lesson_text'|trans({
'%teacherName%': teacherName,
'%recipientDate%': recipientTime|localizeddate('short', 'none', recipientLocale, recipientTime.getTimezone()),
'%recipientTime%': recipientTime|localizeddate('none', 'short', recipientLocale, recipientTime.getTimezone()),
'%my_lessons_link%': absolute_url(path('user.dashboard.current')),
'%teacher_profile_link%': absolute_url(path('teacher.profile.view', {'username': teacherUsername})),
'%faq_link%': absolute_url(path('faq'))
}, 'mail', recipientLanguage)|raw|nl2br }}{% endautoescape %}
            </p>
        </th>
<th class="expander"></th></tr></table></th>
    </tr></tbody></table>
{% endblock %}
